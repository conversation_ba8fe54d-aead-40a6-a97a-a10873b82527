#!/usr/bin/env python3
"""
手动下载 CLIP 模型文件的脚本
使用 requests 分块下载，避免超时问题
"""

import os
import requests
import json
from pathlib import Path

# 设置镜像站
MIRROR_BASE = "https://hf-mirror.com"
MODEL_NAME = "openai/clip-vit-large-patch14"
CACHE_DIR = "/root/.cache/huggingface/transformers"

# 需要下载的关键文件
REQUIRED_FILES = [
    "config.json",
    "pytorch_model.bin",
    "tokenizer_config.json",
    "tokenizer.json",
    "vocab.json",
    "merges.txt",
    "preprocessor_config.json"
]

def download_file(url, local_path, chunk_size=8192):
    """分块下载文件"""
    print(f"📥 下载: {url}")
    print(f"📁 保存到: {local_path}")
    
    try:
        response = requests.get(url, stream=True, verify=False, timeout=30)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r进度: {percent:.1f}% ({downloaded}/{total_size})", end="", flush=True)
        
        print(f"\n✅ 下载完成: {local_path}")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def main():
    print("🚀 开始手动下载 CLIP 模型文件...")
    
    # 创建缓存目录
    cache_path = Path(CACHE_DIR)
    cache_path.mkdir(parents=True, exist_ok=True)
    
    # 生成模型缓存路径 (模拟 transformers 的命名规则)
    model_cache_dir = cache_path / f"models--{MODEL_NAME.replace('/', '--')}"
    model_cache_dir.mkdir(parents=True, exist_ok=True)
    
    snapshots_dir = model_cache_dir / "snapshots" / "main"
    snapshots_dir.mkdir(parents=True, exist_ok=True)
    
    success_count = 0
    
    for filename in REQUIRED_FILES:
        url = f"{MIRROR_BASE}/{MODEL_NAME}/resolve/main/{filename}"
        local_path = snapshots_dir / filename
        
        if download_file(url, str(local_path)):
            success_count += 1
        else:
            print(f"⚠️ 跳过文件: {filename}")
    
    print(f"\n📊 下载结果: {success_count}/{len(REQUIRED_FILES)} 文件成功")
    
    if success_count >= 3:  # 至少需要 config.json, pytorch_model.bin, tokenizer_config.json
        print("✅ 关键文件下载成功，可以尝试加载模型")
        
        # 创建符号链接 (模拟 transformers 的缓存结构)
        refs_dir = model_cache_dir / "refs"
        refs_dir.mkdir(exist_ok=True)
        
        main_ref = refs_dir / "main"
        if not main_ref.exists():
            with open(main_ref, 'w') as f:
                f.write("main")
        
        return True
    else:
        print("❌ 关键文件下载失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

#!/bin/bash

echo "开始构建 Python 运行环境..."

# 构建镜像
echo "构建 Docker 镜像..."
docker build -t ocr-autoscore:3.6 -f Dockerfile --build-arg BUILD_CONTEXT=$(pwd)/.. .

if [ $? -ne 0 ]; then
    echo "Docker 构建失败，请检查错误信息"
    exit 1
fi

# 清理旧容器
echo "清理旧容器..."
docker stop ocr-autoscore 2>/dev/null
docker rm ocr-autoscore 2>/dev/null

# 运行新容器
echo "启动 ocr-autoscore 容器..."
if command -v nvidia-smi &> /dev/null; then
    # 如果有GPU支持
    docker run -d \
        --name ocr-autoscore \
        --gpus all \
        -p 58000:8000 \
        -e PYTHONPATH=/app \
        -e HF_ENDPOINT=https://hf-mirror.com \
        -v $(pwd)/../:/app/ \
        ocr-autoscore:3.6
else
    # 如果没有GPU支持
    echo "警告: 未检测到GPU支持，将以CPU模式运行..."
    docker run -d \
        --name ocr-autoscore \
        -p 58000:8000 \
        -e PYTHONPATH=/app \
        -e HF_ENDPOINT=https://hf-mirror.com \
        -v $(pwd)/../:/app/ \
        ocr-autoscore:3.6
fi

echo "Python 环境构建完成!"
echo "提示：构建score_web 目录需要单独在宿主机环境中运行"
echo "docker logs -f ocr-autoscore 查看服务启动日志:"



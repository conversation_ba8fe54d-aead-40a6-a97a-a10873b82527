#!/bin/bash

echo "==== OCR AutoScore Container Starting ===="
date

# 函数：启动 Layout4Card OCR 服务
start_layout4card() {
    echo "==== Starting Layout4Card OCR Service ===="
    cd /app/segmentation/Layout4Card

    # 检查配置文件是否存在
    if [ ! -f "config.yaml" ]; then
        echo "ERROR: config.yaml not found in Layout4Card directory"
        return 1
    fi

    # 检查模型文件是否存在
    if [ ! -f "./runs/detect/train3/weights/best.pt" ]; then
        echo "ERROR: Layout4Card model weights not found"
        return 1
    fi

    # 后台启动 Layout4Card 服务
    nohup python run.py > /var/log/layout4card.log 2>&1 &
    layout4card_pid=$!
    echo "Layout4Card OCR Service started with PID: $layout4card_pid"

    # 等待服务启动
    sleep 5

    # 检查服务是否正常启动
    if curl -s http://localhost:8891/pic_infer > /dev/null 2>&1; then
        echo "✅ Layout4Card OCR Service is running on port 8891"
    else
        echo "⚠️  Layout4Card OCR Service may not be fully ready yet"
    fi
}

# 函数：启动 Django 服务
start_django() {
    echo "==== Starting Django Service ===="
    cd /app/score_server

    # 检查 Django 项目是否存在
    if [ ! -f "manage.py" ]; then
        echo "ERROR: Django manage.py not found"
        return 1
    fi

    # 启动 Django 服务（前台运行，保持容器活跃）
    python manage.py runserver 0.0.0.0:8000
}

# 启动 Layout4Card OCR 服务
start_layout4card

# 启动 Django 服务（前台运行）
start_django
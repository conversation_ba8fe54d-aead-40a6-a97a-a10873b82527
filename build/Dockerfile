# 基础镜像使用 Python 3.6
FROM python:3.6-slim

# 设置 apt 国内源（中科大）
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list \
    && sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list


# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    g++ \
    libgl1-mesa-glx \
    libpq-dev \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建必要的目录
RUN mkdir -p /app/django_server

# 复制依赖文件
COPY requirements.txt .
COPY ultralytics-8.0.208.tar.gz .

# 设置 pip 国内源（阿里云）
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

RUN pip install --upgrade pip setuptools wheel

# 设置 Hugging Face 镜像站环境变量
ENV HF_ENDPOINT=https://hf-mirror.com

# 安装 huggingface_hub 并下载 CLIP 模型
RUN pip install -U huggingface_hub

# 安装 Python 依赖
RUN pip install  -r requirements.txt

# 手动下载 CLIP 模型文件到缓存目录（使用 wget）
RUN mkdir -p /root/.cache/huggingface/transformers/models--openai--clip-vit-large-patch14/snapshots/main && \
    cd /root/.cache/huggingface/transformers/models--openai--clip-vit-large-patch14/snapshots/main && \
    wget -O config.json "https://hf-mirror.com/openai/clip-vit-large-patch14/resolve/main/config.json" && \
    wget -O pytorch_model.bin "https://hf-mirror.com/openai/clip-vit-large-patch14/resolve/main/pytorch_model.bin" && \
    wget -O tokenizer_config.json "https://hf-mirror.com/openai/clip-vit-large-patch14/resolve/main/tokenizer_config.json" && \
    wget -O tokenizer.json "https://hf-mirror.com/openai/clip-vit-large-patch14/resolve/main/tokenizer.json" && \
    wget -O vocab.json "https://hf-mirror.com/openai/clip-vit-large-patch14/resolve/main/vocab.json" && \
    wget -O merges.txt "https://hf-mirror.com/openai/clip-vit-large-patch14/resolve/main/merges.txt" && \
    wget -O preprocessor_config.json "https://hf-mirror.com/openai/clip-vit-large-patch14/resolve/main/preprocessor_config.json" && \
    echo "CLIP 模型文件下载完成!"

# 安装本地的 ultralytics 包
RUN pip install ultralytics-8.0.208.tar.gz

# 复制应用代码 (从项目根目录复制)
COPY ../.. .


# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# 暴露端口
EXPOSE 8000


# 复制启动脚本到/opt目录 (避免运行时挂载覆盖)
COPY bash.sh /opt/
RUN test -f /opt/bash.sh && \
    chmod +x /opt/bash.sh && \
    echo "bash.sh installed successfully" || \
    (echo "Error: bash.sh not found in build context" && exit 1)

# 启动命令 (保持容器运行，可通过docker exec手动启动服务)
ENTRYPOINT ["/bin/bash", "/opt/bash.sh"]
CMD [ "executable" ]
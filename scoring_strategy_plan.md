# 评分系统修改策略

## 目标
解决 `/api/student/paper/answer/detail?paperId=27&username=z<PERSON><PERSON>` 无法准确打分的问题，特别是针对纯手写内容，优化降级评分逻辑，并确保所有分数保留小数点后两位。

## 问题诊断回顾
*   日志显示 `Layout4Card` 大题分割模型未能检测到有效的答题卡区域，原因是图片为纯手写内容。
*   系统进入降级模式 `_fallback_handwriting_score`。
*   在降级模式下：
    *   选择题 (`xzt`) 被标记为“无法通过手写识别评分”，返回 -1。
    *   作文题 (`zwt`) 成功进行了评分，返回了 `74.73957824707031`。这个分数是 `essay_score_model` 正常计算的结果，并非识别失败的 -1。
*   用户要求将浮点数分数保留小数点后两位。
*   用户反馈填空题答案可能不完全一致，需要通过模型进行识别。

## 修改策略

### 阶段一：优化 `_fallback_handwriting_score` 降级方案以处理纯手写内容

1.  **作文题 (zwt) 处理**：
    *   **现状**：日志显示作文题在降级模式下已成功评分。
    *   **策略**：保持现有 `zwt_score` 逻辑不变，因为它在降级模式下能够正常工作。**新增：在 `zwt_score` 中，对计算出的作文分数进行四舍五入，保留两位小数。**

2.  **填空题 (tkt/tkt_math) 降级处理**：
    *   **现状**：目前在降级模式下直接返回 -1。
    *   **策略**：
        *   对整个图片进行 OCR 识别，获取所有可识别的文本片段及其位置信息。
        *   遍历数据库中的填空题答案 (`section_answer`)。
        *   对于每个正确答案 `ans_val`，从 OCR 识别出的文本片段中筛选出与 `ans_val` 长度相近或初步匹配的候选文本 `rec_text`。
        *   使用 `fill_blank_model.judge_with_clip(ans_val, rec_text, img)` 进行语义相似度判断。`img` 为原始图片或裁剪出的相关区域。
        *   如果 Clip 判断 `ans_val` 与 `rec_text` 更相似（即 `judge_index == 0`），则认为该填空题正确，否则错误。
        *   根据判断结果，为每个填空题给出 0 或 1 的分数。

3.  **选择题 (xzt) 降级处理**：
    *   **现状**：目前在降级模式下直接返回 -1。
    *   **策略**：
        *   对整个图片进行 OCR 识别，获取所有可识别的文本片段及其位置信息。
        *   尝试识别图片中可能表示选择答案的单个字符（如 A, B, C, D）或数字。这可能需要结合 `SingleCharacterRecognition` 模型对 OCR 结果中的单个字符进行二次识别和验证。
        *   根据识别出的手写选项，与数据库中的正确答案进行比对。
        *   根据比对结果，为每个选择题给出 0 或 1 的分数。

### 阶段二：修正 `score.py` 中 `get_score` 函数的答案匹配逻辑

*   **问题**：`get_score` 函数中处理 `fillin_problem` 和 `subjective_problem` 的嵌套循环和 `answer_set_index` 更新逻辑可能导致答案与题目区域的匹配出现问题。
*   **策略**：
    *   重新审视并简化 `get_score` 中 `for answer in self.answer[answer_set_index:]:` 的逻辑。
    *   确保每个 `section_img` (裁剪出的题目区域) 都能与 `self.answer` 中对应的正确答案正确关联。一种更健壮的方法可能是，在 `Layout4Card` 识别出区域后，根据区域的类型和在试卷上的相对位置，动态地从 `self.answer` 中获取对应的答案。如果 `self.answer` 是按照题目顺序排列的，那么 `answer_set_index` 的递增使用是合理的，但需要确保嵌套循环不会导致跳过或重复处理答案。

### 阶段三：代码实现和测试

1.  **修改 `score.py`**：
    *   在 `_fallback_handwriting_score` 中实现填空题和选择题的降级处理逻辑，特别是引入 Clip 进行语义判断和 `SingleCharacterRecognition` 进行字符识别。
    *   优化 `get_score` 中 `answer_set_index` 和答案匹配的逻辑。
    *   在 `zwt_score` 函数中，对计算出的作文分数进行四舍五入，保留两位小数。
2.  **修改 `score_server/index/views.py`**：
    *   在 `getScore` 函数中，对最终的总分 `final_score` 进行四舍五入，保留两位小数。
3.  **测试**：使用纯手写答题图片进行测试，验证降级方案的有效性。

## 计划流程图

```mermaid
graph TD
    A[用户请求 /api/student/paper/answer/detail] --> B{views.py::getScore};
    B --> C[获取学生答题照片和试卷答案];
    C --> D[实例化 scoresystem 并设置答案];
    D --> E{遍历每张答题照片};
    E --> F[score.py::get_score(img)];
    F --> G[图片预处理];
    G --> H{尝试 Layout4Card 大题分割};
    H -- 成功 --> I{根据区域类型调用对应评分函数};
    I --> J[tkt_score / tkt_math_score / zwt_score / xzt_score];
    H -- 失败 (纯手写) --> K[调用 _fallback_handwriting_score];
    K --> L{根据答案配置处理不同题型};
    L -- zwt --> M[OCR 全文识别 & 作文评分 (保持现有逻辑, 分数保留2位小数)];
    L -- xzt --> N[OCR 全文识别 & 识别手写选项 & 比对答案 (新逻辑)];
    L -- tkt/tkt_math --> O[OCR 全文识别 & 文本匹配/语义比对 (Clip) & 比对答案 (新逻辑)];
    J --> P[返回单张照片评分结果];
    M --> P;
    N --> P;
    O --> P;
    P --> Q[views.py::getScore 汇总所有照片评分];
    Q --> R[对最终总分进行四舍五入，保留2位小数];
    R --> S[返回最终总分];
```

# 新功能：在 `score_web` 中添加 `Layout4Card` 训练页面

## 目标
在前端提供一个页面，允许用户上传图片进行 YOLO 打标（LabelMe 格式），然后将标注数据发送到后端，触发 `Layout4Card` 模型的训练。用户应能查看训练进度和结果。训练完成后的模型将自动集成到项目指定位置。

## 核心模块

1.  **前端 (score_web)**：
    *   **新页面/路由**：在 `score_web/src/pages/Teacher/` 下创建一个新页面，例如 `TrainBoard/TrainBoard.tsx`，并添加相应的路由。
    *   **图片上传组件**：允许用户上传原始图片。
    *   **标注文件上传**：允许用户上传与图片对应的 LabelMe JSON 标注文件。
        *   **标注流程说明**：页面将提供清晰的指引，说明用户如何使用外部的 LabelMe 工具（或其他兼容工具）对图片进行标注，并生成对应的 JSON 文件。
        *   **未来增强**：考虑在未来版本中集成一个简化的在线标注工具，允许用户直接在页面上绘制边界框并选择类别。
    *   **训练任务触发**：通过 API 调用后端，触发模型训练。
    *   **训练进度/结果展示**：通过轮询后端 API 获取训练日志文件内容，并展示训练日志、准确率等信息。

2.  **后端 (score_server/index)**：
    *   **数据存储**：在 `settings.py` 中定义一个用于存储训练图片和标注文件的 `MEDIA_ROOT/training_data` 路径。
    *   **训练状态文件**：定义一个文件（例如 `training_status.json`）来记录当前训练任务的状态和日志文件路径。
    *   **新 API 接口**：
        *   `POST /api/train/upload_data`：接收用户上传的图片和 LabelMe 格式的 JSON 标注文件。将这些文件存储在 `media/training_data/` 目录。
        *   `POST /api/train/start_training`：
            *   接收训练参数（例如 `val_size`）。
            *   **检查当前是否有训练任务在运行**：如果正在运行，则拒绝新任务。
            *   **生成唯一的任务 ID**。
            *   **创建训练日志文件**：为当前任务创建一个独立的日志文件（例如 `logs/training_<task_id>.log`）。
            *   **异步执行**：使用 `subprocess.Popen` 在后台运行一个 Python 脚本（例如 `score_server/index/training_worker.py`），该脚本负责：
                *   调用 `python segmentation/Layout4Card/utils/labelme2yolo.py` 将 LabelMe 转换为 YOLO 格式。
                *   调用 `python segmentation/Layout4Card/train.py` 启动模型训练，并将训练日志重定向到任务的日志文件。
                *   **训练完成后，将新模型保存到 `segmentation/Layout4Card/runs/detect/train3/weights/best.pt`。**
                *   在训练开始、结束、失败时更新 `training_status.json` 文件。
            *   更新 `training_status.json` 记录新任务的状态和日志文件路径。
            *   返回训练任务已启动的信息和任务 ID。
        *   `GET /api/train/status`：根据任务 ID（或获取当前活跃任务）读取对应的日志文件内容和 `training_status.json`，返回训练进度和状态。
    *   **新的 Python 脚本 `score_server/index/training_worker.py`**：
        *   这个脚本将作为 `start_training` API 调用的实际执行者，负责调用 `labelme2yolo.py` 和 `train.py`。
        *   它将处理训练过程中的标准输出和错误，并将其写入到指定的日志文件。
        *   在训练的不同阶段更新 `training_status.json`。
    *   **更新 `score_server/index/urls.py`**：添加新的 URL 路由。

3.  **模型训练脚本 (segmentation/Layout4Card)**：
    *   `train.py`：需要确保它能够接收命令行参数来指定输入数据目录和输出模型目录。
    *   `utils/labelme2yolo.py`：需要确保它能够接收命令行参数来指定输入 JSON 目录、输出目录和验证集比例。

## 计划流程图

```mermaid
graph TD
    A[前端用户访问训练页面] --> B{score_web::TrainBoard.tsx};
    B --> C[上传图片和LabelMe JSON];
    C --> D[调用后端API: /api/train/upload_data];
    D --> E[后端保存文件到 media/training_data/];
    E --> F[前端用户输入训练参数];
    F --> G[点击“开始训练”按钮];
    G --> H[调用后端API: /api/train/start_training];
    H --> I{后端检查是否有活跃训练任务};
    I -- 无活跃任务 --> J[后端生成任务ID, 创建日志文件, 更新 training_status.json];
    J --> K[后端启动 training_worker.py (subprocess.Popen)];
    K --> L[training_worker.py 调用 labelme2yolo.py];
    L --> M[training_worker.py 调用 train.py];
    M --> N[训练日志输出到任务日志文件];
    N --> O[training_worker.py 更新 training_status.json];
    O --> P[前端轮询后端API: /api/train/status];
    P --> Q[后端读取任务日志文件和 training_status.json];
    Q --> R[前端展示训练进度和日志];
    M -- 训练完成 --> S[training_worker.py 保存新模型到指定位置];
    S --> T[training_worker.py 更新 training_status.json 为“完成”];
    I -- 有活跃任务 --> W[后端返回“任务正在运行”];
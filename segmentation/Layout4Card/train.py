from ultralytics import YOLO
import yaml
import argparse
import sys
import os

def main():
    parser = argparse.ArgumentParser(description='Train Layout4Card YOLO model')
    parser.add_argument('--data', type=str, required=True, help='Path to data.yaml file')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--imgsz', type=int, default=640, help='Image size for training')
    parser.add_argument('--weights', type=str, default='yolov8n.pt', help='Initial weights path')
    parser.add_argument('--project', type=str, default='runs/detect', help='Project directory')
    parser.add_argument('--name', type=str, default='train', help='Experiment name')
    parser.add_argument('--device', type=str, default='', help='Device to use (cpu, 0, 1, etc.)')

    args = parser.parse_args()

    # 检查数据文件是否存在
    if not os.path.exists(args.data):
        print(f"Error: Data file {args.data} not found!")
        sys.exit(1)

    # 初始化模型
    model = YOLO(args.weights)

    # 开始训练
    print(f"Starting training with:")
    print(f"  Data: {args.data}")
    print(f"  Epochs: {args.epochs}")
    print(f"  Image size: {args.imgsz}")
    print(f"  Weights: {args.weights}")
    print(f"  Project: {args.project}")
    print(f"  Name: {args.name}")

    results = model.train(
        data=args.data,
        epochs=args.epochs,
        imgsz=args.imgsz,
        project=args.project,
        name=args.name,
        device=args.device,
        save=True,
        save_period=10,  # 每10个epoch保存一次
        verbose=True
    )

    print("Training completed!")
    return results

if __name__ == '__main__':
    main()

from ultralytics import YOLO
import os
import cv2
import random
import yaml
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/app/debug.log')
    ]
)


CLS_ID_NAME_MAP = {
    0: 'student_id',
    1: 'subjective_problem',
    2: 'fillin_problem',
    3: 'objective_problem'
}
class OuterSegmentation:
    def __init__(self):
        base_dir = os.path.dirname(os.path.abspath(__file__))
        logging.debug(f"脚本所在目录: {base_dir}")
        
        config_path = os.path.join(base_dir, 'config.yaml')
        logging.debug(f"配置文件路径: {config_path}")
        
        # 验证配置文件是否存在
        if not os.path.exists(config_path):
            logging.error(f"配置文件不存在于: {config_path}")
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path) as f:
            config = yaml.safe_load(f)
            logging.debug(f"配置文件内容: {config}")
            
        model_path = os.path.join(base_dir, config['infer_weights'])
        logging.debug(f"模型文件路径: {model_path}")
        
        if not os.path.exists(model_path):
            logging.error(f"模型文件不存在: {model_path}")
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
        logging.info(f"成功加载模型: {model_path}")
        self.model = YOLO(model=model_path)

    def get_segmentation(self, img):
        results =  self.model.predict(source=img, imgsz=640, save=False)
        return results

if __name__ == '__main__':
    # 获取脚本所在目录作为基准路径
    base_dir = os.path.dirname(os.path.abspath(__file__))
    logging.debug(f"脚本所在目录: {base_dir}")
    
    folder = os.path.join(base_dir, 'testdata')
    logging.debug(f"测试数据目录: {folder}")
    
    if not os.path.exists(folder):
        logging.error(f"测试数据目录不存在: {folder}")
        logging.info(f"请在容器内创建目录: mkdir -p {folder}")
        exit(1)
        
    file_names = os.listdir(folder)
    logging.debug(f"找到测试文件: {file_names}")
    save_folder = os.path.join(base_dir, 'saved')
    if not os.path.isdir(save_folder):
        os.mkdir(save_folder)
    batch_size = 4
    random.shuffle(file_names)
    imgs = []
    results = []
    outer_segmentation = OuterSegmentation()
    for i in range(0, len(file_names), batch_size):
        batch_file_name = file_names[i:i+batch_size]
        for file_name in batch_file_name:
            img_path = os.path.join(folder, file_name)
            img = cv2.imread(img_path)
            imgs += [img]
        results = outer_segmentation.get_segmentation(imgs)

        # 从results中提取出标签为3: 'objective_problem'的box，并从原图中裁剪出来，然后展示到屏幕上
        for result in results:
            for box in result.boxes:
                cls_id = box.cls.cpu().numpy()[0]
                x1,y1,x2,y2 = box.xyxy.cpu().numpy()[0]
                cls_name = CLS_ID_NAME_MAP[cls_id]
                if cls_name == 'fillin_problem':
                    img = result.orig_img
                    img = img[int(y1):int(y2), int(x1):int(x2)]
                    # 保存img到目标文件夹，文件名随机生成
                    cv2.imwrite(os.path.join(save_folder, str(random.randint(0, 1000000))+'.jpg'), img)
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试评分 API 的返回逻辑
"""

import json

def test_score_processing():
    """测试不同评分结果的处理逻辑"""
    
    # 测试用例1：空结果
    scores1 = []
    print("测试用例1 - 空结果:")
    print(f"scores: {scores1}")
    print(f"not scores: {not scores1}")
    print(f"all(not score for score in scores): {all(not score for score in scores1) if scores1 else True}")
    print()
    
    # 测试用例2：包含空列表的结果
    scores2 = [[]]
    print("测试用例2 - 包含空列表:")
    print(f"scores: {scores2}")
    print(f"not scores: {not scores2}")
    print(f"all(not score for score in scores): {all(not score for score in scores2)}")
    print()
    
    # 测试用例3：有效的评分结果
    scores3 = [[{'section': 'zwt', 'value': [74.74]}]]
    print("测试用例3 - 有效评分结果:")
    print(f"scores: {scores3}")
    print(f"not scores: {not scores3}")
    print(f"all(not score for score in scores): {all(not score for score in scores3)}")
    
    # 模拟分数提取
    final_score = 0
    for score_result in scores3:
        if score_result:
            for section in score_result:
                if 'value' in section and section['value']:
                    value = section['value'][0]
                    final_score += float(value)
                    break
            break
    print(f"提取的最终分数: {final_score}")
    print()
    
    # 测试用例4：numpy array 格式
    import numpy as np
    scores4 = [[{'section': 'zwt', 'value': [np.array([74.74])]}]]
    print("测试用例4 - numpy array 格式:")
    print(f"scores: {scores4}")
    
    final_score = 0
    for score_result in scores4:
        if score_result:
            for section in score_result:
                if 'value' in section and section['value']:
                    value = section['value'][0]
                    if hasattr(value, 'item'):  # numpy array
                        final_score += float(value.item())
                    else:
                        final_score += float(value)
                    break
            break
    print(f"提取的最终分数: {final_score}")
    print()

def test_frontend_logic():
    """测试前端逻辑"""
    print("测试前端逻辑:")
    
    # 模拟后端返回的不同响应
    responses = [
        {"msg": "success", "score": 80},
        {"msg": "success", "score": []},
        {"msg": "error", "error": "评分失败"},
        {"msg": "success", "score": "invalid"},
    ]
    
    for i, response in enumerate(responses, 1):
        print(f"响应 {i}: {response}")
        
        # 模拟前端检查逻辑
        if response.get("msg") == "success" and isinstance(response.get("score"), (int, float)):
            print(f"  ✅ 前端会设置分数: {response['score']}")
        elif response.get("msg") == "error":
            print(f"  ❌ 前端会显示错误: {response.get('error', '评分失败')}")
        else:
            print(f"  ❌ 前端会显示格式异常错误")
        print()

if __name__ == "__main__":
    test_score_processing()
    test_frontend_logic()

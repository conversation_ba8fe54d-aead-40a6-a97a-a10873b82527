import os
import sys
import subprocess
import json
import time
from datetime import datetime

# 添加项目根目录到 Python 路径
# 假设 training_worker.py 位于 score_server/index/
# 项目根目录是 score_server 的父目录的父目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from utils.logger import get_django_logger
logger = get_django_logger()

# 导入 Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'score_server.settings')
import django
django.setup()
from django.conf import settings

def update_training_status(task_id, status, message="", progress=0, log_file=None):
    """更新训练状态文件"""
    status_data = {
        "task_id": task_id,
        "status": status,
        "message": message,
        "progress": progress,
        "timestamp": datetime.now().isoformat(),
        "log_file": log_file
    }
    try:
        with open(settings.TRAINING_STATUS_FILE, 'w') as f:
            json.dump(status_data, f, indent=4)
        logger.info(f"训练状态更新: Task ID: {task_id}, Status: {status}, Message: {message}")
    except Exception as e:
        logger.error(f"更新训练状态文件失败: {str(e)}")

def run_training_task(task_id, val_size, training_data_dir, output_model_dir, log_file_path):
    """
    执行训练任务：LabelMe转YOLO，然后训练模型
    """
    logger.info(f"训练任务 {task_id} 开始执行...")
    update_training_status(task_id, "RUNNING", "正在准备数据...", 10, log_file_path)

    # 确保输出模型目录存在
    os.makedirs(output_model_dir, exist_ok=True)

    # 1. LabelMe 转 YOLO
    labelme2yolo_script = os.path.join(project_root, 'segmentation', 'Layout4Card', 'utils', 'labelme2yolo.py')
    yolo_output_dir = os.path.join(training_data_dir, 'yolo_format')
    os.makedirs(yolo_output_dir, exist_ok=True)

    labelme2yolo_cmd = [
        sys.executable, labelme2yolo_script,
        '--json_dir', training_data_dir, # 假设图片和json都在training_data_dir
        '--val_size', str(val_size),
        '--output_dir', yolo_output_dir
    ]
    logger.info(f"执行 LabelMe 转 YOLO 命令: {' '.join(labelme2yolo_cmd)}")
    try:
        with open(log_file_path, 'a') as log_f:
            process = subprocess.run(labelme2yolo_cmd, check=True, capture_output=True, text=True)
            log_f.write("\n--- LabelMe to YOLO Output ---\n")
            log_f.write(process.stdout)
            if process.stderr:
                log_f.write("\n--- LabelMe to YOLO Error ---\n")
                log_f.write(process.stderr)
        logger.info(f"LabelMe 转 YOLO 完成。输出到: {yolo_output_dir}")
        update_training_status(task_id, "RUNNING", "数据转换完成，开始训练模型...", 30, log_file_path)
    except subprocess.CalledProcessError as e:
        logger.error(f"LabelMe 转 YOLO 失败: {e.stderr}")
        update_training_status(task_id, "FAILED", f"数据转换失败: {e.stderr}", 0, log_file_path)
        return
    except Exception as e:
        logger.error(f"LabelMe 转 YOLO 过程中发生未知错误: {str(e)}")
        update_training_status(task_id, "FAILED", f"数据转换未知错误: {str(e)}", 0, log_file_path)
        return

    # 2. 训练模型
    train_script = os.path.join(project_root, 'segmentation', 'Layout4Card', 'train.py')
    
    # YOLOv8 train.py 通常需要一个 data.yaml 文件来指定数据集路径
    # 这里需要确保 labelme2yolo.py 生成了正确的 data.yaml
    # 假设 data.yaml 也在 yolo_output_dir 中
    data_yaml_path = os.path.join(yolo_output_dir, 'data.yaml') 
    if not os.path.exists(data_yaml_path):
        logger.error(f"未找到数据集配置文件: {data_yaml_path}")
        update_training_status(task_id, "FAILED", "数据集配置文件缺失", 0, log_file_path)
        return

    # YOLOv8 训练命令示例
    # python train.py --data data.yaml --epochs 100 --weights yolov8n.pt --project runs/detect --name train_custom
    # 我们需要将训练结果保存到指定位置，并覆盖原有的 best.pt
    # YOLOv8 默认会将模型保存到 runs/detect/expN/weights/best.pt
    # 我们需要将这个 best.pt 复制到 output_model_dir (即 segmentation/Layout4Card/runs/detect/train3/weights/)
    
    # 为了简化，我们直接指定 project 和 name，让 YOLOv8 保存到我们期望的路径
    # 注意：YOLOv8 每次训练会创建新的 exp 目录 (exp, exp2, exp3...)
    # 更好的做法是让 train.py 接受一个输出目录参数，或者训练完成后手动复制
    # 鉴于用户要求直接集成到指定位置，我们尝试直接控制 YOLOv8 的输出路径
    
    # 假设 output_model_dir 是 segmentation/Layout4Card/runs/detect/train3/weights/
    # 那么 YOLOv8 的 project 应该是 segmentation/Layout4Card/runs/detect/train3/
    # 并且 name 应该是 weights
    # 这种方式可能不直接，因为 YOLOv8 倾向于创建新的 runs/detect/expN 目录
    
    # 更稳妥的方案是：让 YOLOv8 正常训练到其默认输出目录，然后我们手动复制 best.pt
    yolov8_runs_dir = os.path.join(project_root, 'segmentation', 'Layout4Card', 'runs')
    
    train_cmd = [
        sys.executable, train_script,
        '--data', data_yaml_path,
        '--epochs', '50', # 示例：可以作为参数传入
        '--imgsz', '640', # 示例：可以作为参数传入
        '--weights', 'yolov8n.pt', # 初始权重，可以从网上下载或使用预训练模型
        '--project', os.path.join(yolov8_runs_dir, 'detect'), # 指定项目目录
        '--name', 'train_custom_model' # 指定训练名称，会创建 runs/detect/train_custom_model
    ]
    logger.info(f"执行训练命令: {' '.join(train_cmd)}")
    
    try:
        with open(log_file_path, 'a') as log_f:
            process = subprocess.Popen(train_cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            for line in process.stdout:
                log_f.write(line)
                log_f.flush() # 实时写入日志
            process.wait() # 等待进程结束
            if process.returncode != 0:
                raise subprocess.CalledProcessError(process.returncode, train_cmd, output=process.stdout.read())
        
        logger.info(f"模型训练完成。")
        update_training_status(task_id, "RUNNING", "模型训练完成，正在集成...", 90, log_file_path)

        # 查找最新的训练结果目录，并复制 best.pt
        latest_exp_dir = None
        detect_runs_dir = os.path.join(yolov8_runs_dir, 'detect')
        if os.path.exists(detect_runs_dir):
            exp_dirs = [d for d in os.listdir(detect_runs_dir) if os.path.isdir(os.path.join(detect_runs_dir, d)) and d.startswith('train_custom_model')]
            exp_dirs.sort(key=lambda x: os.path.getmtime(os.path.join(detect_runs_dir, x)), reverse=True)
            if exp_dirs:
                latest_exp_dir = os.path.join(detect_runs_dir, exp_dirs[0])
        
        if latest_exp_dir and os.path.exists(os.path.join(latest_exp_dir, 'weights', 'best.pt')):
            source_model_path = os.path.join(latest_exp_dir, 'weights', 'best.pt')
            destination_model_path = os.path.join(output_model_dir, 'best.pt')
            
            import shutil
            shutil.copy(source_model_path, destination_model_path)
            logger.info(f"新模型已集成到: {destination_model_path}")
            update_training_status(task_id, "COMPLETED", "模型训练与集成成功", 100, log_file_path)
        else:
            logger.error("未找到训练完成的 best.pt 模型文件。")
            update_training_status(task_id, "FAILED", "模型文件未找到", 0, log_file_path)

    except subprocess.CalledProcessError as e:
        logger.error(f"模型训练失败: {e.output}")
        update_training_status(task_id, "FAILED", f"模型训练失败: {e.output}", 0, log_file_path)
    except Exception as e:
        logger.error(f"模型训练过程中发生未知错误: {str(e)}")
        update_training_status(task_id, "FAILED", f"模型训练未知错误: {str(e)}", 0, log_file_path)

if __name__ == '__main__':
    # 示例用法 (在实际Django环境中由API调用)
    if len(sys.argv) > 5:
        task_id = sys.argv[1]
        val_size = float(sys.argv[2])
        training_data_dir = sys.argv[3]
        output_model_dir = sys.argv[4]
        log_file_path = sys.argv[5]
        run_training_task(task_id, val_size, training_data_dir, output_model_dir, log_file_path)
    else:
        logger.error("参数不足。用法: python training_worker.py <task_id> <val_size> <training_data_dir> <output_model_dir> <log_file_path>")
        # 示例调试运行
        # update_training_status("debug_task_123", "INITIALIZED", "调试任务初始化", 0, "debug_log.log")
        # run_training_task("debug_task_123", 0.2, "/path/to/your/training_data", "/path/to/your/output_model", "debug_log.log")
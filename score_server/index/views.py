import json
import os
import sys
import subprocess
import uuid
from datetime import datetime
import PIL.Image
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from index.models import Student, Teacher, Paper, PaperPhoto, Problem, Answer, StudentUploadAnswerPhoto
from utils.util import tid_maker

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.logger import get_django_logger

# 初始化日志记录器
logger = get_django_logger()

# 延迟导入 score 模块，避免在 Django 启动时初始化 PaddleOCR


@require_http_methods(["POST"])
def login(request):
    response = {}
    username = request.POST["username"]
    identity = request.POST["identity"]
    password = request.POST["password"]
    logger.debug(f"登录请求: {request.POST}")
    if identity == 'student':
        student = Student.objects.filter(username=username, password=password)
        if not student:
            response['msg'] = '用户名或密码错误'
        else:
            response['msg'] = 'success'
    else:
        teacher = Teacher.objects.filter(username=username, password=password)
        if not teacher:
            response['msg'] = '用户名或密码错误'
        else:
            response['msg'] = 'success'
    return JsonResponse(response)


@require_http_methods(["POST"])
def register(request):
    response = {}
    username = request.POST["username"]
    identity = request.POST["identity"]
    password = request.POST["password"]
    email = request.POST["email"]
    school = request.POST["school"]
    if identity == 'student':
        student = Student.objects.filter(username=username, email=email)
        if student:
            response["msg"] = "用户名或者邮箱已经被注册过"
            return JsonResponse(response)
        Student.objects.create(username=username, password=password, email=email, school=school)
    else:
        teacher = Teacher.objects.filter(username=username, password=password)
        if teacher:
            response["msg"] = "用户名或者邮箱已经被注册过"
            return JsonResponse(response)
        Teacher.objects.create(username=username, password=password, email=email, school=school)
    response["msg"] = "success"
    return JsonResponse(response)


@require_http_methods(["GET"])
def addPaper(request):
    username = request.GET["username"]
    teacher = Teacher.objects.filter(username=username)[0]
    paper = Paper.objects.create(teacher=teacher)
    return JsonResponse({"msg": "success", "data": {"paperId": paper.id}})


@require_http_methods(["GET"])
def removePaper(request):
    pid = request.GET["paperId"]
    Paper.objects.get(id=pid).delete()
    return JsonResponse({"msg": "success"})


def image_upload(file_obj, folder):
    name = tid_maker() + '.' + file_obj.name.split(".")[1]
    file_name = settings.MEDIA_ROOT + '/{}/'.format(folder) + name
    with open(file_name, "wb") as f:
        for line in file_obj:
            f.write(line)
    return {"filename": file_name, "name": name}


@require_http_methods(["POST"])
def paper_image_upload(request):
    file_obj = request.FILES.get("upload_image")
    name_obj = image_upload(file_obj, "paper")
    logger.debug(f"试卷图片上传: {name_obj}")
    paper_id = request.POST["paperId"]
    paper = Paper.objects.get(id=paper_id)
    PaperPhoto.objects.create(photoPath="/paper/" + name_obj["name"], paper=paper)
    return JsonResponse({"msg": 'success', 'data': {
        'url': request.build_absolute_uri("/media/paper/" + name_obj["name"]), 'name': name_obj["name"]}})


@require_http_methods(["POST"])
def student_image_upload(request):
    file_obj = request.FILES.get("upload_image")
    name_obj = image_upload(file_obj, "studentAns")
    paper_id = request.POST["paperId"]
    username = request.POST["username"]
    paper = Paper.objects.get(id=paper_id)
    student = Student.objects.get(username=username)
    StudentUploadAnswerPhoto.objects.create(photoPath="/studentAns/" + name_obj["name"], paper=paper, student=student)
    return JsonResponse({"msg": 'success', 'data': {
        'url': request.build_absolute_uri("/media/studentAns/" + name_obj["name"]), 'name': name_obj["name"]}})


@require_http_methods(["POST"])
def image_delete(request):
    pass

@require_http_methods(["POST"])
def upload_training_data(request):
    """
    上传训练图片和LabelMe JSON标注文件
    """
    response = {}
    uploaded_files = request.FILES.getlist("files") # 支持多文件上传
    
    if not uploaded_files:
        response['msg'] = '没有接收到文件'
        return JsonResponse(response, status=400)

    # 确保训练数据目录存在
    os.makedirs(settings.TRAINING_DATA_ROOT, exist_ok=True)

    for file_obj in uploaded_files:
        file_path = os.path.join(settings.TRAINING_DATA_ROOT, file_obj.name)
        try:
            with open(file_path, "wb+") as destination:
                for chunk in file_obj.chunks():
                    destination.write(chunk)
            logger.info(f"文件上传成功: {file_path}")
        except Exception as e:
            logger.error(f"文件上传失败: {file_obj.name} - {str(e)}")
            response['msg'] = f"文件上传失败: {file_obj.name}"
            return JsonResponse(response, status=500)
    
    response['msg'] = '文件上传成功'
    return JsonResponse(response)


@require_http_methods(["POST"])
def start_training(request):
    """
    启动 Layout4Card 模型训练任务
    """
    response = {}
    try:
        body = json.loads(request.body)
        val_size = body.get("val_size", 0.2) # 默认验证集比例 0.2

        # 检查当前是否有训练任务在运行
        if os.path.exists(settings.TRAINING_STATUS_FILE):
            with open(settings.TRAINING_STATUS_FILE, 'r') as f:
                status_data = json.load(f)
            if status_data.get("status") == "RUNNING":
                response['msg'] = '已有训练任务正在运行，请稍后再试。'
                return JsonResponse(response, status=409) # Conflict
        
        task_id = str(uuid.uuid4()) # 生成唯一的任务 ID
        log_file_name = f"training_{task_id}.log"
        log_file_path = os.path.join(settings.BASE_DIR, 'logs', log_file_name) # 日志文件存储在项目根目录的 logs 文件夹下
        os.makedirs(os.path.dirname(log_file_path), exist_ok=True) # 确保日志目录存在

        # 更新初始状态
        initial_status = {
            "task_id": task_id,
            "status": "INITIALIZING",
            "message": "训练任务初始化中...",
            "progress": 0,
            "timestamp": datetime.now().isoformat(),
            "log_file": log_file_path
        }
        with open(settings.TRAINING_STATUS_FILE, 'w') as f:
            json.dump(initial_status, f, indent=4)
        
        # 异步启动 training_worker.py
        training_worker_script = os.path.join(settings.BASE_DIR, 'score_server', 'index', 'training_worker.py')
        training_data_dir = settings.TRAINING_DATA_ROOT
        output_model_dir = os.path.join(settings.BASE_DIR, 'segmentation', 'Layout4Card', 'runs', 'detect', 'train3', 'weights') # 训练完成后的模型保存路径

        cmd = [
            sys.executable, training_worker_script,
            task_id,
            str(val_size),
            training_data_dir,
            output_model_dir,
            log_file_path
        ]
        
        # 使用 subprocess.Popen 在后台运行，不阻塞当前请求
        subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL) # 将输出重定向到 DEVNULL，由 worker 脚本写入日志文件

        response['msg'] = '训练任务已成功启动'
        response['data'] = {'task_id': task_id}
        return JsonResponse(response)

    except json.JSONDecodeError:
        response['msg'] = '请求体格式错误，请提供有效的 JSON'
        return JsonResponse(response, status=400)
    except Exception as e:
        logger.error(f"启动训练任务失败: {str(e)}")
        response['msg'] = f"启动训练任务失败: {str(e)}"
        return JsonResponse(response, status=500)

@require_http_methods(["GET"])
def get_training_status(request):
    """
    获取训练任务状态和日志
    """
    response = {}
    try:
        if not os.path.exists(settings.TRAINING_STATUS_FILE):
            response['msg'] = '没有活跃的训练任务'
            response['data'] = {'status': 'IDLE', 'message': '无任务', 'progress': 0, 'log_content': ''}
            return JsonResponse(response)
        
        with open(settings.TRAINING_STATUS_FILE, 'r') as f:
            status_data = json.load(f)
        
        log_content = ""
        if status_data.get("log_file") and os.path.exists(status_data["log_file"]):
            with open(status_data["log_file"], 'r') as log_f:
                log_content = log_f.read()
        
        response['msg'] = 'success'
        response['data'] = {
            'task_id': status_data.get("task_id"),
            'status': status_data.get("status"),
            'message': status_data.get("message"),
            'progress': status_data.get("progress"),
            'timestamp': status_data.get("timestamp"),
            'log_content': log_content
        }
        return JsonResponse(response)

    except Exception as e:
        logger.error(f"获取训练状态失败: {str(e)}")
        response['msg'] = f"获取训练状态失败: {str(e)}"
        return JsonResponse(response, status=500)

@require_http_methods(["POST"])
def ans_set(request):
    body = json.loads(request.body)
    logger.debug(f"设置答案请求: {body}")
    # 删除之前的答案
    paper_id = body["paperId"]
    paper = Paper.objects.get(id=paper_id)
    Problem.objects.filter(paper=paper).delete()

    # 新设置问题和答案
    answer_list = body["answerList"]
    type_list = body["typeList"]

    for ans_index in range(len(answer_list)):
        problem_type = type_list[ans_index]
        problem = Problem.objects.create(paper=paper, type=problem_type)
        for a in answer_list[ans_index]:
            Answer.objects.create(problem=problem, answer=a)

    return JsonResponse({"msg": "success"})


@require_http_methods(["GET"])
def setPaperName(request):
    paper_id = request.GET["paperId"]
    paper_name = request.GET["paperName"]
    paper = Paper.objects.get(id=paper_id)
    paper.name = paper_name
    paper.save()
    return JsonResponse({"msg": "success"})


@require_http_methods(["GET"])
def showPapersForTeacher(request):
    username = request.GET["username"]
    teacher = Teacher.objects.get(username=username)
    papers = Paper.objects.filter(teacher=teacher, name__isnull=False)
    return JsonResponse(
        {"msg": "success", "papers": [{"title": paper.name,
                                     "time": paper.created_at.strftime("%Y-%m-%d %H:%M"),
                                     "id": paper.id} for paper in papers]})


@require_http_methods(["GET"])
def showPaperForStudent(request):
    papers = Paper.objects.filter(name__isnull=False)
    return JsonResponse(
        {"msg": "success", "papers": [{"title": paper.name,
                                     "time": paper.created_at.strftime("%Y-%m-%d %H:%M"),
                                     "id": paper.id, 
                                     "teacher": paper.teacher.username}
                                    for paper in papers]})


@require_http_methods(["GET"])
def showPaperDetail(request):
    root_url = request.scheme + '://' + request.get_host()
    paper_id = request.GET["paperId"]
    paper = Paper.objects.get(id=paper_id)
    photos = PaperPhoto.objects.filter(paper=paper)
    return JsonResponse({"msg": "success", "paperImages": [
        {"url": root_url + settings.MEDIA_URL + "paper/" + photo.photoPath.split("/")[-1], "uid": photo.id}
        for photo in photos
    ]})


@require_http_methods(["GET"])
def showPaperAnsDetail(request):
    root_url = request.scheme + '://' + request.get_host()
    paper_id = request.GET["paperId"]
    username = request.GET["username"]
    student = Student.objects.get(username=username)
    paper = Paper.objects.get(id=paper_id)
    photos = StudentUploadAnswerPhoto.objects.filter(paper=paper, student=student)
    return JsonResponse({"msg": "success", "answerImages": [
        {"url": root_url + settings.MEDIA_URL + "studentAns/" + photo.photoPath.split("/")[-1], "uid": photo.id}
        for photo in photos
    ]})


@require_http_methods(["GET"])
def deletePaperAnsPhoto(request):
    answer_id = request.GET["answerId"]
    photo = StudentUploadAnswerPhoto.objects.get(id=answer_id)
    if photo:
        try:
            file_path = settings.MEDIA_ROOT + photo.photoPath
            if os.path.exists(file_path):
                os.remove(file_path)
            photo.delete()
            return JsonResponse({"msg": "success"})
        except Exception as e:
            return JsonResponse({"msg": str(e)})
    else:
        return JsonResponse({"msg": "fail"})


@require_http_methods(["GET"])
def getScore(request):
    # 在函数内部导入 score 模块，避免在 Django 启动时初始化 PaddleOCR
    from score import scoresystem

    paper_id = request.GET["paperId"]
    username = request.GET["username"]
    logger.info(f"开始评分: paperId={paper_id}, username={username}")

    student = Student.objects.get(username=username)
    paper = Paper.objects.get(id=paper_id)
    photos = StudentUploadAnswerPhoto.objects.filter(paper=paper, student=student)
    logger.info(f"找到学生答题照片: {len(photos)} 张")

    problems = Problem.objects.filter(paper=paper)
    answers_list = []
    for problem in problems:
        answer_obj_list = Answer.objects.filter(problem=problem)
        answers = {'section': problem.type, 'value': []}
        for a in answer_obj_list:
            answers['value'].append(a.answer)
        answers_list.append(answers)

    logger.info(f"试卷答案配置: {answers_list}")

    # 调用模型
    s = scoresystem()
    s.set_answer(answers_list)
    scores = []

    for i, photo in enumerate(photos):
        logger.info(f"正在处理第 {i+1}/{len(photos)} 张照片: {photo.photoPath}")
        try:
            img = PIL.Image.open(settings.MEDIA_ROOT + photo.photoPath)
            total_result = s.get_score(img)
            logger.info(f"照片 {i+1} 评分结果: {total_result}")
            scores.append(total_result)
        except Exception as e:
            logger.error(f"处理照片 {photo.photoPath} 时出错: {str(e)}")
            scores.append([])

    logger.info(f"评分完成，总分数: {scores}")

    # 检查评分结果是否有效
    if not scores or all(not score for score in scores):
        logger.warning("评分失败：没有获得有效的评分结果，返回 -1 分")
        return JsonResponse({"msg": "success", "score": -1})

    # 计算总分（优先返回有效分数，只有全部失败时才返回 -1）
    final_score = 0
    has_valid_score = False
    failed_sections = []

    for score_result in scores:
        if score_result:
            for section in score_result:
                if 'value' in section and section['value']:
                    # 处理不同的分数格式
                    value = section['value'][0]
                    section_name = section.get('section', 'unknown')

                    # 检查是否为 -1（识别失败标记）
                    if value == -1:
                        logger.info(f"section {section_name} 识别失败")
                        failed_sections.append(section_name)
                        continue

                    # 处理正常分数
                    if hasattr(value, 'item'):  # numpy array
                        score_value = float(value.item())
                    else:
                        score_value = float(value)

                    logger.info(f"section {section_name} 评分成功: {score_value}")
                    final_score += score_value
                    has_valid_score = True
    final_score = round(final_score, 2)

    # 如果没有找到任何有效分数，返回 -1
    if not has_valid_score:
        logger.warning("所有题目识别失败，返回 -1 分")
        final_score = -1
    else:
        logger.info(f"成功评分，失败的题目: {failed_sections}")
        # 如果有部分成功，返回成功的分数

    logger.info(f"最终分数: {final_score}")
    return JsonResponse({"msg": "success", "score": final_score})


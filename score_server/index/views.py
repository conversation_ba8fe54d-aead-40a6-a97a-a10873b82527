import json
import os
import sys
from datetime import datetime
import PIL.Image
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from index.models import Student, Teacher, Paper, PaperPhoto, Problem, Answer, StudentUploadAnswerPhoto
from utils.util import tid_maker

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.logger import get_django_logger

# 初始化日志记录器
logger = get_django_logger()

# 延迟导入 score 模块，避免在 Django 启动时初始化 PaddleOCR


@require_http_methods(["POST"])
def login(request):
    response = {}
    username = request.POST["username"]
    identity = request.POST["identity"]
    password = request.POST["password"]
    logger.debug(f"登录请求: {request.POST}")
    if identity == 'student':
        student = Student.objects.filter(username=username, password=password)
        if not student:
            response['msg'] = '用户名或密码错误'
        else:
            response['msg'] = 'success'
    else:
        teacher = Teacher.objects.filter(username=username, password=password)
        if not teacher:
            response['msg'] = '用户名或密码错误'
        else:
            response['msg'] = 'success'
    return JsonResponse(response)


@require_http_methods(["POST"])
def register(request):
    response = {}
    username = request.POST["username"]
    identity = request.POST["identity"]
    password = request.POST["password"]
    email = request.POST["email"]
    school = request.POST["school"]
    if identity == 'student':
        student = Student.objects.filter(username=username, email=email)
        if student:
            response["msg"] = "用户名或者邮箱已经被注册过"
            return JsonResponse(response)
        Student.objects.create(username=username, password=password, email=email, school=school)
    else:
        teacher = Teacher.objects.filter(username=username, password=password)
        if teacher:
            response["msg"] = "用户名或者邮箱已经被注册过"
            return JsonResponse(response)
        Teacher.objects.create(username=username, password=password, email=email, school=school)
    response["msg"] = "success"
    return JsonResponse(response)


@require_http_methods(["GET"])
def addPaper(request):
    username = request.GET["username"]
    teacher = Teacher.objects.filter(username=username)[0]
    paper = Paper.objects.create(teacher=teacher)
    return JsonResponse({"msg": "success", "data": {"paperId": paper.id}})


@require_http_methods(["GET"])
def removePaper(request):
    pid = request.GET["paperId"]
    Paper.objects.get(id=pid).delete()
    return JsonResponse({"msg": "success"})


def image_upload(file_obj, folder):
    name = tid_maker() + '.' + file_obj.name.split(".")[1]
    file_name = settings.MEDIA_ROOT + '/{}/'.format(folder) + name
    with open(file_name, "wb") as f:
        for line in file_obj:
            f.write(line)
    return {"filename": file_name, "name": name}


@require_http_methods(["POST"])
def paper_image_upload(request):
    file_obj = request.FILES.get("upload_image")
    name_obj = image_upload(file_obj, "paper")
    logger.debug(f"试卷图片上传: {name_obj}")
    paper_id = request.POST["paperId"]
    paper = Paper.objects.get(id=paper_id)
    PaperPhoto.objects.create(photoPath="/paper/" + name_obj["name"], paper=paper)
    return JsonResponse({"msg": 'success', 'data': {
        'url': request.build_absolute_uri("/media/paper/" + name_obj["name"]), 'name': name_obj["name"]}})


@require_http_methods(["POST"])
def student_image_upload(request):
    file_obj = request.FILES.get("upload_image")
    name_obj = image_upload(file_obj, "studentAns")
    paper_id = request.POST["paperId"]
    username = request.POST["username"]
    paper = Paper.objects.get(id=paper_id)
    student = Student.objects.get(username=username)
    StudentUploadAnswerPhoto.objects.create(photoPath="/studentAns/" + name_obj["name"], paper=paper, student=student)
    return JsonResponse({"msg": 'success', 'data': {
        'url': request.build_absolute_uri("/media/studentAns/" + name_obj["name"]), 'name': name_obj["name"]}})


@require_http_methods(["POST"])
def image_delete(request):
    pass


@require_http_methods(["POST"])
def ans_set(request):
    body = json.loads(request.body)
    logger.debug(f"设置答案请求: {body}")
    # 删除之前的答案
    paper_id = body["paperId"]
    paper = Paper.objects.get(id=paper_id)
    Problem.objects.filter(paper=paper).delete()

    # 新设置问题和答案
    answer_list = body["answerList"]
    type_list = body["typeList"]

    for ans_index in range(len(answer_list)):
        problem_type = type_list[ans_index]
        problem = Problem.objects.create(paper=paper, type=problem_type)
        for a in answer_list[ans_index]:
            Answer.objects.create(problem=problem, answer=a)

    return JsonResponse({"msg": "success"})


@require_http_methods(["GET"])
def setPaperName(request):
    paper_id = request.GET["paperId"]
    paper_name = request.GET["paperName"]
    paper = Paper.objects.get(id=paper_id)
    paper.name = paper_name
    paper.save()
    return JsonResponse({"msg": "success"})


@require_http_methods(["GET"])
def showPapersForTeacher(request):
    username = request.GET["username"]
    teacher = Teacher.objects.get(username=username)
    papers = Paper.objects.filter(teacher=teacher, name__isnull=False)
    return JsonResponse(
        {"msg": "success", "papers": [{"title": paper.name,
                                     "time": paper.created_at.strftime("%Y-%m-%d %H:%M"),
                                     "id": paper.id} for paper in papers]})


@require_http_methods(["GET"])
def showPaperForStudent(request):
    papers = Paper.objects.filter(name__isnull=False)
    return JsonResponse(
        {"msg": "success", "papers": [{"title": paper.name,
                                     "time": paper.created_at.strftime("%Y-%m-%d %H:%M"),
                                     "id": paper.id, 
                                     "teacher": paper.teacher.username}
                                    for paper in papers]})


@require_http_methods(["GET"])
def showPaperDetail(request):
    root_url = request.scheme + '://' + request.get_host()
    paper_id = request.GET["paperId"]
    paper = Paper.objects.get(id=paper_id)
    photos = PaperPhoto.objects.filter(paper=paper)
    return JsonResponse({"msg": "success", "paperImages": [
        {"url": root_url + settings.MEDIA_URL + "paper/" + photo.photoPath.split("/")[-1], "uid": photo.id}
        for photo in photos
    ]})


@require_http_methods(["GET"])
def showPaperAnsDetail(request):
    root_url = request.scheme + '://' + request.get_host()
    paper_id = request.GET["paperId"]
    username = request.GET["username"]
    student = Student.objects.get(username=username)
    paper = Paper.objects.get(id=paper_id)
    photos = StudentUploadAnswerPhoto.objects.filter(paper=paper, student=student)
    return JsonResponse({"msg": "success", "answerImages": [
        {"url": root_url + settings.MEDIA_URL + "studentAns/" + photo.photoPath.split("/")[-1], "uid": photo.id}
        for photo in photos
    ]})


@require_http_methods(["GET"])
def deletePaperAnsPhoto(request):
    answer_id = request.GET["answerId"]
    photo = StudentUploadAnswerPhoto.objects.get(id=answer_id)
    if photo:
        try:
            file_path = settings.MEDIA_ROOT + photo.photoPath
            if os.path.exists(file_path):
                os.remove(file_path)
            photo.delete()
            return JsonResponse({"msg": "success"})
        except Exception as e:
            return JsonResponse({"msg": str(e)})
    else:
        return JsonResponse({"msg": "fail"})


@require_http_methods(["GET"])
def getScore(request):
    # 在函数内部导入 score 模块，避免在 Django 启动时初始化 PaddleOCR
    from score import scoresystem

    paper_id = request.GET["paperId"]
    username = request.GET["username"]
    logger.info(f"开始评分: paperId={paper_id}, username={username}")

    student = Student.objects.get(username=username)
    paper = Paper.objects.get(id=paper_id)
    photos = StudentUploadAnswerPhoto.objects.filter(paper=paper, student=student)
    logger.info(f"找到学生答题照片: {len(photos)} 张")

    problems = Problem.objects.filter(paper=paper)
    answers_list = []
    for problem in problems:
        answer_obj_list = Answer.objects.filter(problem=problem)
        answers = {'section': problem.type, 'value': []}
        for a in answer_obj_list:
            answers['value'].append(a.answer)
        answers_list.append(answers)

    logger.info(f"试卷答案配置: {answers_list}")

    # 调用模型
    s = scoresystem()
    s.set_answer(answers_list)
    scores = []

    for i, photo in enumerate(photos):
        logger.info(f"正在处理第 {i+1}/{len(photos)} 张照片: {photo.photoPath}")
        try:
            img = PIL.Image.open(settings.MEDIA_ROOT + photo.photoPath)
            total_result = s.get_score(img)
            logger.info(f"照片 {i+1} 评分结果: {total_result}")
            scores.append(total_result)
        except Exception as e:
            logger.error(f"处理照片 {photo.photoPath} 时出错: {str(e)}")
            scores.append([])

    logger.info(f"评分完成，总分数: {scores}")

    # 检查评分结果是否有效
    if not scores or all(not score for score in scores):
        logger.warning("评分失败：没有获得有效的评分结果")
        return JsonResponse({"msg": "error", "error": "评分失败，请检查上传的图片是否为标准答题卡格式"})

    # 计算总分（简化处理，取第一个有效分数）
    final_score = 0
    for score_result in scores:
        if score_result:
            for section in score_result:
                if 'value' in section and section['value']:
                    # 处理不同的分数格式
                    value = section['value'][0]
                    if hasattr(value, 'item'):  # numpy array
                        final_score += float(value.item())
                    else:
                        final_score += float(value)
                    break
            break

    logger.info(f"最终分数: {final_score}")
    return JsonResponse({"msg": "success", "score": final_score})


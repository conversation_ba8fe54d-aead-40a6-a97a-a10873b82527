"""score_server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf.urls import url
from django.contrib import admin
from django.urls import path, include, re_path
from django.views.static import serve
from django.views.generic import TemplateView

from score_server import settings

urlpatterns = [
    path('admin/', admin.site.urls),
    path("api/", include('index.urls')),
    url(r'media/(?P<path>.*)', serve, {"document_root": settings.MEDIA_ROOT}),
    re_path("^$", TemplateView.as_view(template_name="index.html")),
    # Catch-all pattern for React Router (must be last)
    re_path(r'^.*/$', TemplateView.as_view(template_name="index.html")),
]

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试训练 API 功能
"""

import requests
import json
import time
import os

API_BASE_URL = "http://localhost:58000/api"

def test_training_status():
    """测试训练状态 API"""
    print("=== 测试训练状态 API ===")
    
    response = requests.get(f"{API_BASE_URL}/train/status")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_upload_training_data():
    """测试上传训练数据 API"""
    print("=== 测试上传训练数据 API ===")
    
    # 创建一个简单的测试文件
    test_file_content = "这是一个测试文件"
    test_file_path = "/tmp/test_training_file.txt"
    
    with open(test_file_path, 'w') as f:
        f.write(test_file_content)
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'files': ('test_file.txt', f, 'text/plain')}
            response = requests.post(f"{API_BASE_URL}/train/upload_data", files=files)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
    except Exception as e:
        print(f"上传失败: {e}")
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
    
    print()

def test_start_training():
    """测试启动训练 API"""
    print("=== 测试启动训练 API ===")
    
    data = {
        "val_size": 0.2
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/train/start_training",
            json=data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            # 如果启动成功，等待一段时间后检查状态
            print("等待 5 秒后检查训练状态...")
            time.sleep(5)
            test_training_status()
            
    except Exception as e:
        print(f"启动训练失败: {e}")
    
    print()

def test_training_workflow():
    """测试完整的训练工作流程"""
    print("=== 测试完整训练工作流程 ===")
    
    # 1. 检查初始状态
    print("1. 检查初始状态")
    test_training_status()
    
    # 2. 上传测试数据
    print("2. 上传测试数据")
    test_upload_training_data()
    
    # 3. 启动训练（这可能会失败，因为没有真实的训练数据）
    print("3. 尝试启动训练")
    test_start_training()

if __name__ == "__main__":
    print("开始测试训练 API...")
    print("=" * 50)
    
    test_training_workflow()
    
    print("=" * 50)
    print("测试完成！")

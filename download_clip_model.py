#!/usr/bin/env python3
"""
使用 HF-Mirror 下载 CLIP 模型的脚本
按照 https://hf-mirror.com 官方推荐的方式
"""

import os
import sys

# 设置 HF-Mirror 环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['HUGGINGFACE_HUB_CACHE'] = '/root/.cache/huggingface'

print("🚀 开始使用 HF-Mirror 下载 CLIP 模型...")
print(f"📍 HF_ENDPOINT: {os.environ['HF_ENDPOINT']}")
print(f"📁 缓存目录: {os.environ['HUGGINGFACE_HUB_CACHE']}")

try:
    from transformers import CLIPModel, CLIPProcessor
    import torch
    
    print("📦 正在下载 CLIP 模型...")
    model = CLIPModel.from_pretrained('openai/clip-vit-large-patch14')
    print("✅ CLIP 模型下载成功!")
    
    print("📦 正在下载 CLIP 处理器...")
    processor = CLIPProcessor.from_pretrained('openai/clip-vit-large-patch14')
    print("✅ CLIP 处理器下载成功!")
    
    print("🎯 测试模型加载...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    print(f"✅ 模型成功加载到 {device}")
    
    print("🧪 测试模型功能...")
    # 简单测试
    import numpy as np
    from PIL import Image
    
    # 创建一个测试图像
    test_image = Image.fromarray(np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8))
    test_texts = ["a photo", "an image"]
    
    inputs = processor(text=test_texts, images=test_image, return_tensors="pt", padding=True)
    inputs = {k: v.to(device) for k, v in inputs.items()}
    
    with torch.no_grad():
        outputs = model(**inputs)
        logits_per_image = outputs.logits_per_image
        probs = logits_per_image.softmax(dim=1)
        print(f"✅ 模型测试成功! 输出形状: {probs.shape}")
    
    print("🎉 所有测试通过! CLIP 模型已成功下载并可以使用!")
    
except Exception as e:
    print(f"❌ 下载失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

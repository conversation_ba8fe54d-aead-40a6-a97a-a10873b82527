#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一日志配置模块
提供项目级别的日志配置和管理
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    # ANSI 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logger(name, level=logging.INFO, log_file=None, console=True):
    """
    设置日志记录器
    
    Args:
        name (str): 日志记录器名称
        level (int): 日志级别
        log_file (str): 日志文件路径，None 表示不写文件
        console (bool): 是否输出到控制台
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    
    # 日志格式
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器（带颜色）
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        
        # 使用带颜色的格式化器
        colored_formatter = ColoredFormatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(colored_formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # 使用 RotatingFileHandler 进行日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name, level=logging.INFO):
    """
    获取日志记录器的便捷函数
    
    Args:
        name (str): 日志记录器名称
        level (int): 日志级别
    
    Returns:
        logging.Logger: 日志记录器
    """
    # 根据环境变量决定日志级别
    env_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    if env_level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
        level = getattr(logging, env_level)
    
    # 日志文件路径
    log_dir = os.getenv('LOG_DIR', '/var/log/ocr-autoscore')
    log_file = os.path.join(log_dir, f'{name}.log')
    
    return setup_logger(name, level=level, log_file=log_file, console=True)


# 预定义的日志记录器
def get_main_logger():
    """获取主应用日志记录器"""
    return get_logger('ocr-autoscore')


def get_ocr_logger():
    """获取 OCR 服务日志记录器"""
    return get_logger('ocr-service')


def get_layout_logger():
    """获取 Layout4Card 服务日志记录器"""
    return get_logger('layout4card')


def get_score_logger():
    """获取评分模型日志记录器"""
    return get_logger('score-model')


def get_django_logger():
    """获取 Django 服务日志记录器"""
    return get_logger('django-service')


# 设置第三方库的日志级别
def configure_third_party_loggers():
    """配置第三方库的日志级别，减少噪音"""
    # 设置第三方库日志级别为 WARNING，减少信息输出
    third_party_loggers = [
        'urllib3.connectionpool',
        'requests.packages.urllib3',
        'transformers.tokenization_utils_base',
        'transformers.configuration_utils',
        'transformers.modeling_utils',
        'PIL.PngImagePlugin',
        'matplotlib.font_manager',
    ]
    
    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(logging.WARNING)


# 初始化时配置第三方库日志
configure_third_party_loggers()

#!/usr/bin/env python3
"""
只测试 CLIP 模型加载部分
"""

import os
import sys
import time

# 设置镜像站
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

sys.path.append('/app')
from scoreblocks.fillblankmodel import setup_hf_mirror

print("🚀 测试 CLIP 模型加载...")

# 设置镜像站
hf_endpoint = setup_hf_mirror()
print(f'[INFO] 使用 Hugging Face 镜像站: {hf_endpoint}')

# 测试本地缓存加载
local_model_path = '/root/.cache/huggingface/transformers/models--openai--clip-vit-large-patch14/snapshots/main'
if os.path.exists(local_model_path):
    print('[INFO] 发现本地缓存，尝试从本地加载模型...')
    try:
        from transformers import CLIPModel, CLIPProcessor
        import torch
        
        print('[DEBUG] 正在从本地加载 CLIP 模型...')
        clip_model = CLIPModel.from_pretrained(local_model_path)
        print('[DEBUG] 正在从本地加载 CLIP 处理器...')
        clip_processor = CLIPProcessor.from_pretrained(local_model_path)
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        clip_model = clip_model.to(device)
        print(f'[DEBUG] ✅ CLIP模型从本地缓存加载成功! 设备: {device}')
        
        # 简单测试
        print('🧪 测试模型功能...')
        import numpy as np
        from PIL import Image
        
        test_image = Image.fromarray(np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8))
        test_texts = ["a photo", "an image"]
        
        inputs = clip_processor(text=test_texts, images=test_image, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = clip_model(**inputs)
            logits_per_image = outputs.logits_per_image
            probs = logits_per_image.softmax(dim=1)
            print(f'✅ 模型测试成功! 输出形状: {probs.shape}')
        
        print('🎉 CLIP 模型完全可用!')
        
    except Exception as e:
        print(f'❌ 本地缓存加载失败: {e}')
        import traceback
        traceback.print_exc()
else:
    print('❌ 本地缓存不存在')
    print(f'期望路径: {local_model_path}')
    
    # 检查实际存在的文件
    cache_base = '/root/.cache/huggingface'
    if os.path.exists(cache_base):
        import glob
        all_files = glob.glob(f'{cache_base}/**/*', recursive=True)
        print('实际缓存文件:')
        for f in all_files[:10]:  # 只显示前10个
            print(f'  - {f}')

.online-annotator {
  .annotator-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }

  .annotator-workspace {
    display: flex;
    gap: 16px;
    
    .canvas-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      
      canvas {
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
    
    .shapes-list {
      flex: 0 0 250px;
      
      .ant-list-item {
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
        
        &:hover {
          background: #fafafa;
        }
      }
      
      .ant-list-item-action {
        margin-left: 8px;
      }
    }
  }

  .annotation-instructions {
    margin-bottom: 16px;
    padding: 12px;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    
    .instruction-title {
      font-weight: 600;
      color: #0050b3;
      margin-bottom: 8px;
    }
    
    .instruction-list {
      margin: 0;
      padding-left: 20px;
      color: #1890ff;
      
      li {
        margin-bottom: 4px;
      }
    }
  }

  .upload-for-annotation {
    margin-bottom: 16px;
    
    .ant-upload-drag {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      transition: border-color 0.3s;
      
      &:hover {
        border-color: #1890ff;
      }
    }
    
    .upload-hint {
      margin-top: 8px;
      color: #8c8c8c;
      font-size: 12px;
      text-align: center;
    }
  }

  .annotation-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    
    .stat-item {
      padding: 8px 16px;
      background: #f6f8fa;
      border-radius: 6px;
      text-align: center;
      
      .stat-number {
        font-size: 20px;
        font-weight: 600;
        color: #1890ff;
        display: block;
      }
      
      .stat-label {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  .category-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      
      .color-box {
        width: 16px;
        height: 16px;
        border-radius: 3px;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }
      
      .label-text {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .export-section {
    margin-top: 16px;
    padding: 16px;
    background: #f6f8fa;
    border-radius: 6px;
    border: 1px solid #e1e4e8;
    
    .export-title {
      font-weight: 600;
      margin-bottom: 8px;
      color: #24292e;
    }
    
    .export-description {
      color: #586069;
      font-size: 13px;
      margin-bottom: 12px;
    }
    
    .export-actions {
      display: flex;
      gap: 8px;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .annotator-workspace {
      flex-direction: column;
      
      .shapes-list {
        flex: none;
        width: 100%;
      }
    }
  }

  @media (max-width: 768px) {
    .annotator-controls {
      flex-direction: column;
      align-items: stretch;
      
      .ant-space {
        justify-content: center;
      }
    }
    
    .canvas-container canvas {
      max-width: 100%;
      height: auto;
    }
  }
}

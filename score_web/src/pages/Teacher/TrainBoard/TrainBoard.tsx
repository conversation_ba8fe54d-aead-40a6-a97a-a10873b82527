import React, { useState, useEffect, useRef } from 'react';
import { Upload, Button, message, InputNumber, Spin, Progress, Card, Typography, Row, Col, Space, Tag, Alert, Divider } from 'antd';
import { UploadOutlined, PlayCircleOutlined, SyncOutlined, StopOutlined, DeleteOutlined, InboxOutlined } from '@ant-design/icons';
import axios from 'axios';
import './TrainBoard.less';

const { Title, Paragraph } = Typography;

interface TrainingStatus {
  task_id: string;
  status: string;
  message: string;
  progress: number;
  timestamp: string;
  log_content: string;
}

const TrainBoard: React.FC = () => {
  const [fileList, setFileList] = useState<any[]>([]);
  const [valSize, setValSize] = useState<number>(0.2);
  const [trainingStatus, setTrainingStatus] = useState<TrainingStatus | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const logContainerRef = useRef<HTMLDivElement>(null);

  const API_BASE_URL = process.env.NODE_ENV === 'development' ? 'http://localhost:8000' : '';

  // 文件上传前的处理
  const beforeUpload = (file: any) => {
    setFileList(prevList => [...prevList, file]);
    return false; // 阻止自动上传
  };

  // 移除文件
  const onRemove = (file: any) => {
    const index = fileList.indexOf(file);
    const newFileList = fileList.slice();
    newFileList.splice(index, 1);
    setFileList(newFileList);
  };

  // 上传文件到后端
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择要上传的文件！');
      return;
    }

    setLoading(true);
    const formData = new FormData();
    fileList.forEach(file => {
      formData.append('files', file);
    });

    try {
      const response = await axios.post(`${API_BASE_URL}/api/train/upload_data`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (response.data.msg === '文件上传成功') {
        message.success('文件上传成功，可以开始训练！');
        setFileList([]); // 清空文件列表
      } else {
        message.error(`文件上传失败: ${response.data.msg}`);
      }
    } catch (error: any) {
      message.error(`文件上传失败: ${error.response?.data?.msg || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 启动训练任务
  const startTraining = async () => {
    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/train/start_training`, { val_size: valSize });
      if (response.data.msg === '训练任务已成功启动') {
        message.success('训练任务已启动！');
        setTrainingStatus({ ...trainingStatus, status: 'INITIALIZING', message: '训练任务初始化中...', progress: 0, task_id: response.data.data.task_id } as TrainingStatus);
        fetchTrainingStatus(); // 立即获取一次状态
      } else {
        message.error(`启动训练失败: ${response.data.msg}`);
      }
    } catch (error: any) {
      message.error(`启动训练失败: ${error.response?.data?.msg || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 获取训练状态
  const fetchTrainingStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/train/status`);
      if (response.data.msg === 'success') {
        setTrainingStatus(response.data.data);
      } else {
        message.warning(response.data.msg);
        setTrainingStatus(null); // 没有活跃任务时清空状态
      }
    } catch (error: any) {
      message.error(`获取训练状态失败: ${error.response?.data?.msg || error.message}`);
      setTrainingStatus(null);
    }
  };

  // 定时获取训练状态
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (trainingStatus?.status === 'RUNNING' || trainingStatus?.status === 'INITIALIZING') {
      intervalId = setInterval(fetchTrainingStatus, 3000); // 每3秒更新一次
    } else if (trainingStatus?.status === 'COMPLETED' || trainingStatus?.status === 'FAILED') {
      clearInterval(intervalId!); // 任务完成或失败后停止轮询
    }
    return () => clearInterval(intervalId!);
  }, [trainingStatus]);

  // 组件加载时立即获取一次状态
  useEffect(() => {
    fetchTrainingStatus();
  }, []);

  // 滚动日志到底部
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [trainingStatus?.log_content]);

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      'RUNNING': { color: 'processing', text: '训练中' },
      'COMPLETED': { color: 'success', text: '已完成' },
      'FAILED': { color: 'error', text: '失败' },
      'INITIALIZING': { color: 'warning', text: '初始化中' },
      'PENDING': { color: 'default', text: '等待中' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  return (
    <div className="train-board-container">
      <div className="page-header" style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Typography.Title level={2} style={{ margin: 0 }}>Layout4Card 模型训练</Typography.Title>
            <Typography.Text type="secondary">上传训练数据并启动模型训练任务</Typography.Text>
          </div>
          <Button icon={<SyncOutlined />} onClick={fetchTrainingStatus} loading={loading}>
            刷新状态
          </Button>
        </div>
      </div>

      {trainingStatus && (
        <Alert
          message={
            <Space>
              <span>当前训练状态:</span>
              {getStatusTag(trainingStatus.status)}
              <span>{trainingStatus.message}</span>
            </Space>
          }
          type={trainingStatus.status === 'COMPLETED' ? 'success' :
                trainingStatus.status === 'FAILED' ? 'error' : 'info'}
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Card title="1. 上传训练数据" className="upload-section">
        <Alert
          message="数据准备说明"
          description={
            <div>
              <p>请使用 <a href="https://github.com/wkentaro/labelme" target="_blank" rel="noopener noreferrer">LabelMe</a> 工具对答题卡图片进行标注：</p>
              <ul>
                <li>标注答题卡的各个区域（选择题、填空题、主观题等）</li>
                <li>确保每张图片都有对应的 JSON 标注文件</li>
                <li>支持的图片格式：JPG、PNG、JPEG</li>
                <li>建议图片分辨率：1024x768 或更高</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Upload.Dragger
          multiple
          beforeUpload={beforeUpload}
          onRemove={onRemove}
          fileList={fileList}
          accept=".jpg,.jpeg,.png,.json"
          style={{ marginBottom: 16 }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传图片和JSON标注文件
          </p>
        </Upload.Dragger>

        <div className="upload-hint">
          已选择 {fileList.length} 个文件
        </div>

        <Space style={{ marginTop: 16 }}>
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={handleUpload}
            disabled={fileList.length === 0}
            loading={loading}
          >
            上传文件 ({fileList.length})
          </Button>
          <Button
            icon={<DeleteOutlined />}
            onClick={() => setFileList([])}
            disabled={fileList.length === 0}
          >
            清空列表
          </Button>
        </Space>
      </Card>

      <Card title="2. 训练参数配置">
        <Row gutter={[16, 16]} style={{ marginBottom: 20 }}>
          <Col span={12}>
            <div>
              <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                验证集比例 (val_size):
              </label>
              <InputNumber
                min={0.01}
                max={0.99}
                step={0.01}
                value={valSize}
                onChange={(value) => setValSize(value as number)}
                style={{ width: '100%' }}
                placeholder="0.2"
              />
              <div style={{ fontSize: 12, color: '#8c8c8c', marginTop: 4 }}>
                建议值：0.1-0.3，用于验证模型性能
              </div>
            </div>
          </Col>
          <Col span={12}>
            <div>
              <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                训练状态:
              </label>
              <div className={`status-indicator status-${trainingStatus?.status?.toLowerCase() || 'idle'}`}>
                {trainingStatus ? getStatusTag(trainingStatus.status) : <Tag>空闲</Tag>}
                <span>{trainingStatus?.message || '等待开始训练'}</span>
              </div>
            </div>
          </Col>
        </Row>

        <Divider />

        <div className="training-controls">
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={startTraining}
            loading={loading || trainingStatus?.status === 'RUNNING' || trainingStatus?.status === 'INITIALIZING'}
            disabled={trainingStatus?.status === 'RUNNING' || trainingStatus?.status === 'INITIALIZING'}
          >
            开始训练
          </Button>
          <Button
            icon={<SyncOutlined />}
            onClick={fetchTrainingStatus}
            loading={loading}
          >
            刷新状态
          </Button>
          {trainingStatus?.status === 'RUNNING' && (
            <Button
              danger
              icon={<StopOutlined />}
              onClick={() => message.info('停止功能开发中...')}
            >
              停止训练
            </Button>
          )}
        </div>
      </Card>

      <Card title="3. 训练进度与日志">
        {trainingStatus ? (
          <div>
            <Row gutter={[16, 16]} style={{ marginBottom: 20 }}>
              <Col span={8}>
                <div>
                  <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 4 }}>任务ID</div>
                  <div style={{ fontFamily: 'monospace', fontSize: 13 }}>
                    {trainingStatus.task_id || 'N/A'}
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 4 }}>当前状态</div>
                  <div>{getStatusTag(trainingStatus.status)}</div>
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 4 }}>最后更新</div>
                  <div style={{ fontSize: 13 }}>
                    {trainingStatus.timestamp ? new Date(trainingStatus.timestamp).toLocaleString() : 'N/A'}
                  </div>
                </div>
              </Col>
            </Row>

            {trainingStatus.progress > 0 && (
              <div className="progress-section" style={{ marginBottom: 20 }}>
                <Progress
                  percent={trainingStatus.progress}
                  status={trainingStatus.status === 'FAILED' ? 'exception' :
                          trainingStatus.status === 'COMPLETED' ? 'success' : 'active'}
                  strokeColor={trainingStatus.status === 'FAILED' ? '#ff4d4f' :
                              trainingStatus.status === 'COMPLETED' ? '#52c41a' : '#1890ff'}
                />
                <div className="progress-info">
                  <span>训练进度</span>
                  <span>{trainingStatus.progress}%</span>
                </div>
              </div>
            )}

            <Divider orientation="left">训练日志</Divider>
            <div ref={logContainerRef} className="training-log-container">
              {trainingStatus.log_content ? (
                <pre>{trainingStatus.log_content}</pre>
              ) : (
                <div style={{ textAlign: 'center', color: '#8c8c8c', padding: '40px 0' }}>
                  等待训练日志...
                </div>
              )}
            </div>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#8c8c8c', padding: '40px 0' }}>
            <InboxOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>没有活跃的训练任务</div>
            <div style={{ fontSize: 12, marginTop: 8 }}>上传训练数据后即可开始训练</div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default TrainBoard;
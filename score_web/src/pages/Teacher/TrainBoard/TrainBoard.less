.train-board-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);

  .page-header {
    margin-bottom: 24px;

    .ant-page-header-heading-title {
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }

    .ant-page-header-heading-sub-title {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    margin-bottom: 16px;

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .upload-section {
    .ant-upload-list {
      margin-bottom: 16px;
    }

    .ant-upload-drag {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      transition: border-color 0.3s;

      &:hover {
        border-color: #1890ff;
      }
    }

    .upload-hint {
      margin-top: 8px;
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .training-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;

    .ant-btn {
      border-radius: 6px;
    }

    .ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }

  .training-log-container {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 16px;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.45;
    white-space: pre-wrap;
    word-wrap: break-word;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .log-line {
      margin-bottom: 2px;

      &.log-info {
        color: #0366d6;
      }

      &.log-warning {
        color: #f66a0a;
      }

      &.log-error {
        color: #d73a49;
      }

      &.log-success {
        color: #28a745;
      }
    }
  }

  .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;

    &.status-idle {
      background: #f6f8fa;
      color: #586069;
    }

    &.status-training {
      background: #fff3cd;
      color: #856404;
    }

    &.status-success {
      background: #d4edda;
      color: #155724;
    }

    &.status-error {
      background: #f8d7da;
      color: #721c24;
    }
  }

  .progress-section {
    .ant-progress {
      margin-bottom: 8px;
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}
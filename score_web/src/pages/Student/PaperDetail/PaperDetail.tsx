import React,{useState, useEffect, useCallback} from 'react';
import { Button, Image, Popconfirm, message } from 'antd'
import { useParams } from "react-router-dom";
import './PaperDetail.less'
import axios from 'axios';
import ImageUpload from '@/components/ImageUpload/ImageUpload';
import type { UploadFile } from 'antd/es/upload/interface';

const PaperDetail: React.FC = () => {
    const paperId = parseInt(useParams<{id: string}>()["id"]);
    const [paperImages, setImageList] = useState([]);
    const [open, setOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [score, setScore] = useState(-1);
    const [answers, setAnswers] = useState<UploadFile[]>([]);
    const [loading, setLoading] = useState(false);

    const getPaperPhotos = useCallback(async () => {
        if(loading) return;
        setLoading(true);
        try {
            const result = await axios.request({
                url:"student/paper/detail",
                method: "GET",
                params: {paperId}
            });
            if(result.data.msg === 'success') {
                setImageList(result.data.paperImages);
            }
        } catch (error) {
            console.error('获取试卷图片失败:', error);
            messageApi.error('获取试卷图片失败');
        } finally {
            setLoading(false);
        }
    }, [paperId, loading, messageApi]);

    const getPaperAnswersBefore = useCallback(async () => {
        if(loading) return;
        setLoading(true);
        try {
            const result = await axios.request({
                url: "student/paper/answer/detail",
                method: "GET",
                params: {
                    paperId: paperId,
                    username: window.sessionStorage.getItem('username')
                }
            });
            if(result.data.msg === 'success') {
                setAnswers(result.data.answerImages);
            }
        } catch (error) {
            console.error('获取答案失败:', error);
            messageApi.error('获取答案失败');
        } finally {
            setLoading(false);
        }
    }, [paperId, loading, messageApi]);

    useEffect(() => {
        if (paperId) {
            getPaperPhotos();
            getPaperAnswersBefore();
        }
    }, [paperId, getPaperPhotos, getPaperAnswersBefore]);

    const waitingForScore = async () => {
        if(loading) return;
        setLoading(true);
        setOpen(false);
        messageApi.open({
            type: 'loading',
            content: '正在评分中',
            duration: 0
        });

        try {
            const result = await axios.request({
                url: 'student/paper/score',
                method: 'GET',
                params: {
                    paperId: paperId,
                    username: window.sessionStorage.getItem('username')
                }
            });

            if (result.data.msg === 'success' && typeof result.data.score === 'number') {
                messageApi.destroy();
                setScore(result.data.score);
                messageApi.success(`评分完成！您的分数是 ${result.data.score} 分`);
            } else if (result.data.msg === 'error') {
                messageApi.destroy();
                messageApi.error(result.data.error || '评分失败');
                throw new Error(result.data.error || '评分失败');
            } else {
                messageApi.destroy();
                messageApi.error('评分失败，返回数据格式异常');
                throw new Error('评分失败，返回数据格式异常');
            }
        } catch (error) {
            console.error('评分过程出错:', error);
            messageApi.error('评分失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    }

    const handleAnswerChange = (newFiles: UploadFile[]): void => {
        setAnswers(newFiles);
    };

    const handlePhotoRemove = useCallback(async (file: UploadFile) => {
        if(loading) return;
        setLoading(true);
        try {
            await axios.request({
                method: 'GET', 
                url: 'student/paper/answer/delete',
                params: {
                    "answerId": file.uid
                }
            });
        } catch (error) {
            console.error('删除答案失败:', error);
            messageApi.error('删除答案失败');
        } finally {
            setLoading(false);
        }
    }, [loading, messageApi]);

    return (
        <div className="student_paper_detail">
            <Image.PreviewGroup
                preview={{
                    onChange: (current, prev) => console.log(`current index: ${current}, prev index: ${prev}`),
                }}
            >
                {paperImages.map(item => (
                    <Image 
                        width={200} 
                        src={item.url} 
                        rootClassName='paper_image' 
                        key={item.uid}
                    />
                ))}
            </Image.PreviewGroup>
            <div>
                <h3>请上传自己的答案</h3>
                <ImageUpload 
                    data={{
                        paperId, 
                        "username": window.sessionStorage.getItem("username")
                    }} 
                    url={window.location.origin + '/api/student/answer/imageUpload'} 
                    showUploadButton={score<0}
                    fileList={answers}
                    onFileChange={handleAnswerChange}
                    handleFileRemove={handlePhotoRemove}
                /> 
            </div>
            {score < 0 ? (
                <div className="button_group">
                    <Popconfirm
                        title="提醒"
                        description="确定要提交答案, 不检查一下？"
                        open={open}
                        onConfirm={waitingForScore}
                        onCancel={()=>setOpen(false)}
                    >
                        <Button 
                            type="primary" 
                            onClick={()=>setOpen(true)}
                            disabled={loading}
                        >
                            提交答案
                        </Button>
                    </Popconfirm>
                </div>
            ) : (
                <div>
                    <h3>分数</h3>
                    <p>你的分数是 {score}</p>
                </div>
            )}
            {contextHolder}
        </div>
    )
}

export default PaperDetail
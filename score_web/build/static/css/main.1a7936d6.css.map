{"version": 3, "file": "static/css/main.1a7936d6.css", "mappings": "AAAA,KAKI,kCAAmC,CACnC,iCAAkC,CAJlC,oIADA,QAIJ,CAIA,KACI,uEAFJ,CAOC,gBAEC,wBAA2B,CAD3B,WAFF,CAMA,YACE,YAAa,CAEb,eAAgB,CADhB,UAHF,CCnBA,YAKI,kBAAmB,CAFnB,YAAa,CADb,WAAY,CAEZ,sBAAuB,CAHvB,UAKJ,CCNA,uBAII,qBAAsB,CAFtB,WAAY,CACZ,YAAa,CAFb,UAIJ,CALA,+BASQ,mBAFA,YAAa,CACb,6BAER,CAVA,qCAYY,cACZ,CAbA,6CAiBY,kBAAmB,CADnB,YACZ,CCjBA,+CAEQ,iBAAmB,CACnB,qBAAsB,CAEtB,kBAAmB,CACnB,eAAgB,CAFhB,YAER,CANA,kDASY,mBADA,YAEZ,CAVA,iDAEQ,iBAAmB,CACnB,qBAAsB,CAEtB,kBAAmB,CACnB,eAAgB,CAFhB,YAaR,CAjBA,oDASY,mBADA,YAaZ,CArBA,mEAeQ,eASR,CAxBA,wEAiBY,kBAUZ,CC3BA,sCAGQ,YAAa,CACb,uBACA,eAAgB,CAHhB,UAGR,CCLA,kCACI,YAAa,CAEb,QAAS,CADT,0BAA2B,CAE3B,eACJ,CALA,iDAQQ,kBAAmB,CAFnB,YAAa,CAGb,OAAQ,CAFR,0BAIR,CAXA,qDAYY,WAAY,CADZ,UAIZ,CAfA,sDAeY,cAGZ,CClBA,uBAGE,aAAc,CADd,gBAAiB,CAEjB,8BAA+B,CAH/B,YAIF,CALA,oCAOI,kBACJ,CARA,mEAYM,aAAc,CAFd,cAAe,CACf,eAEN,CAbA,uEAgBM,aAAc,CACd,cAAN,CAjBA,iCAsBI,iBAAkB,CAClB,8BAAyC,CACzC,kBAFJ,CAtBA,gDA2BM,+BAFN,CAzBA,qEA+BQ,aAAc,CADd,eADR,CA7BA,wDAsCM,kBANN,CAhCA,wDA4CM,kBAAmB,CAFnB,yBAA0B,CAC1B,iBAAkB,CAElB,2BAPN,CASM,8DACE,oBAPR,CAzCA,oDAsDM,aAAc,CACd,cAAe,CAFf,cAPN,CA9CA,0CA4DI,YAAa,CACb,QAAS,CACT,kBAXJ,CAnDA,mDAiEM,iBAXN,CAtDA,2DAqEM,kBAAmB,CACnB,oBAZN,CAcM,iEACE,kBAAmB,CACnB,oBAZR,CA9DA,+CA0FI,oBAAqB,CAVrB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAIlB,sFAAyG,CACzG,cAAe,CACf,gBAAiB,CAJjB,gBAAiB,CACjB,eAAgB,CAFhB,YAAa,CAMb,oBAdJ,CAiBI,kEACE,SAfN,CAkBI,wEACE,kBAAmB,CACnB,iBAhBN,CAmBI,wEACE,kBAAmB,CACnB,iBAjBN,CAmBM,8EACE,kBAjBR,CAzFA,yDA+GM,iBAnBN,CAqBM,kEACE,aAnBR,CAsBM,qEACE,aApBR,CAuBM,mEACE,aArBR,CAwBM,qEACE,aAtBR,CAxGA,yCAqII,kBAAmB,CAGnB,kBAAmB,CAJnB,mBAAoB,CAKpB,cAAe,CACf,eAAgB,CAJhB,OAAQ,CACR,gBAtBJ,CA2BI,qDACE,kBAAmB,CACnB,aAzBN,CA4BI,yDACE,kBAAmB,CACnB,aA1BN,CA6BI,wDACE,kBAAmB,CACnB,aA3BN,CA8BI,sDACE,kBAAmB,CACnB,aA5BN,CAjIA,uDAmKM,iBA/BN,CApIA,wDA0KM,aAAc,CAHd,YAAa,CAEb,cAAe,CADf,6BA9BN,CC1IA,wBAII,qBAAsB,CAFtB,WAAY,CACZ,YAAa,CAFb,UAIJ,CALA,gCAQQ,mBAFA,YAAa,CACb,6BAGR,CAVA,sCAWY,cAEZ,CCbA,sBACI,cACJ,CAFA,mCAIQ,kBAAmB,CADnB,iBAGR,CANA,oCAOQ,YAAa,CACb,sBAAuB,CACvB,eAER", "sources": ["index.less", "pages/Login/Login.less", "pages/Teacher/DashBoard/DashBoard.less", "pages/Teacher/AddPaperBoard/AddPaperBoard.less", "pages/Teacher/PaperBoard/PaperBoard.less", "components/PaperList/PaperList.less", "pages/Teacher/TrainBoard/TrainBoard.less", "pages/Student/DashBoard/DashBoard.less", "pages/Student/PaperDetail/PaperDetail.less"], "sourcesContent": ["body {\n    margin: 0;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n        sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n        monospace;\n}\n\n// 为了让组件铺满整个页面\n #root,body,html {\n  height: 100%;\n  background-color: aliceblue;\n}\n\n.ant-layout {\n  display: flex;\n  width: 100%;\n  min-height: 100%;\n}\n\n@primary-color: #1677ff;", ".login_page {\n    width: 100%;\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n@primary-color: #1677ff;", ".techer_dashboard_body {\n    width: 100%;\n    height: 100%;\n    padding: 20px;\n    box-sizing: border-box;\n    \n    .header {\n        display: flex;\n        justify-content:space-between;\n        align-items:center;\n\n        .name {\n            font-size: 14px;\n        }\n\n        .header-right {\n            display: flex;\n            align-items: center;\n        }\n    }\n}\n@primary-color: #1677ff;", ".teacher_add_paper_board_body {\n    .photo_container{\n        border: 1px dashed ;\n        box-sizing: border-box;\n        padding: 20px;\n        margin-bottom: 30px;\n        margin-top: 10px;    \n        h4 {\n            margin-top: 0;\n            margin-bottom:10px;\n        }\n    }\n    .content_container{\n       .photo_container(); \n       .answer_container{\n        margin-top: 10px;\n        .tip {\n            margin-bottom: 10px;\n        }\n       }\n    }\n}\n@primary-color: #1677ff;", ".teacher_PaperBoard {\n    .button_container {\n        width: 100%;\n        display: flex;\n        justify-content:center;\n        margin-top: 40px;\n    }\n}\n@primary-color: #1677ff;", ".paper_list_description_container{\n    display: flex;\n    justify-content: flex-start;\n    gap: 24px;\n    margin-top: 23px;\n    .tag_container {\n        display: flex;\n        justify-content: flex-start;\n        align-items: center;\n        gap: 4px;\n        img {\n            width: 15px;\n            height: 15px;\n        }\n        span {\n            font-size: 12px;\n        }\n    }\n}\n@primary-color: #1677ff;", ".train-board-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  min-height: calc(100vh - 120px);\n\n  .page-header {\n    margin-bottom: 24px;\n\n    .ant-page-header-heading-title {\n      font-size: 24px;\n      font-weight: 600;\n      color: #262626;\n    }\n\n    .ant-page-header-heading-sub-title {\n      color: #8c8c8c;\n      font-size: 14px;\n    }\n  }\n\n  .ant-card {\n    border-radius: 8px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);\n    margin-bottom: 16px;\n\n    .ant-card-head {\n      border-bottom: 1px solid #f0f0f0;\n\n      .ant-card-head-title {\n        font-weight: 600;\n        color: #262626;\n      }\n    }\n  }\n\n  .upload-section {\n    .ant-upload-list {\n      margin-bottom: 16px;\n    }\n\n    .ant-upload-drag {\n      border: 2px dashed #d9d9d9;\n      border-radius: 6px;\n      background: #fafafa;\n      transition: border-color 0.3s;\n\n      &:hover {\n        border-color: #1890ff;\n      }\n    }\n\n    .upload-hint {\n      margin-top: 8px;\n      color: #8c8c8c;\n      font-size: 12px;\n    }\n  }\n\n  .training-controls {\n    display: flex;\n    gap: 12px;\n    margin-bottom: 16px;\n\n    .ant-btn {\n      border-radius: 6px;\n    }\n\n    .ant-btn-primary {\n      background: #1890ff;\n      border-color: #1890ff;\n\n      &:hover {\n        background: #40a9ff;\n        border-color: #40a9ff;\n      }\n    }\n  }\n\n  .training-log-container {\n    background-color: #f6f8fa;\n    border: 1px solid #e1e4e8;\n    border-radius: 6px;\n    padding: 16px;\n    max-height: 500px;\n    overflow-y: auto;\n    font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', 'Monaco', 'Courier New', monospace;\n    font-size: 13px;\n    line-height: 1.45;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n\n    &::-webkit-scrollbar {\n      width: 8px;\n    }\n\n    &::-webkit-scrollbar-track {\n      background: #f1f1f1;\n      border-radius: 4px;\n    }\n\n    &::-webkit-scrollbar-thumb {\n      background: #c1c1c1;\n      border-radius: 4px;\n\n      &:hover {\n        background: #a8a8a8;\n      }\n    }\n\n    .log-line {\n      margin-bottom: 2px;\n\n      &.log-info {\n        color: #0366d6;\n      }\n\n      &.log-warning {\n        color: #f66a0a;\n      }\n\n      &.log-error {\n        color: #d73a49;\n      }\n\n      &.log-success {\n        color: #28a745;\n      }\n    }\n  }\n\n  .status-indicator {\n    display: inline-flex;\n    align-items: center;\n    gap: 8px;\n    padding: 4px 12px;\n    border-radius: 12px;\n    font-size: 12px;\n    font-weight: 500;\n\n    &.status-idle {\n      background: #f6f8fa;\n      color: #586069;\n    }\n\n    &.status-training {\n      background: #fff3cd;\n      color: #856404;\n    }\n\n    &.status-success {\n      background: #d4edda;\n      color: #155724;\n    }\n\n    &.status-error {\n      background: #f8d7da;\n      color: #721c24;\n    }\n  }\n\n  .progress-section {\n    .ant-progress {\n      margin-bottom: 8px;\n    }\n\n    .progress-info {\n      display: flex;\n      justify-content: space-between;\n      font-size: 12px;\n      color: #8c8c8c;\n    }\n  }\n}\n@primary-color: #1677ff;", ".student_dashboard_body {\n    width: 100%;\n    height: 100%;\n    padding: 20px;\n    box-sizing: border-box;\n    .header {\n        display: flex;\n        justify-content:space-between;\n        align-items:center;\n\n        .name {\n            font-size: 14px;\n        }\n    }\n}\n@primary-color: #1677ff;", ".student_paper_detail {\n    margin-top: 5px;\n    .paper_image {\n        margin-right: 12px;\n        margin-bottom: 12px;\n    }\n    .button_group {\n        display: flex;\n        justify-content: center;\n        margin-top: 15px\n    }\n}\n@primary-color: #1677ff;"], "names": [], "sourceRoot": ""}
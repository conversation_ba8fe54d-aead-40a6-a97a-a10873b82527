import React, { useState, useRef, useEffect } from 'react';
import { Card, Button, Select, Input, message, Space, Divider, List, Tag, Modal } from 'antd';
import { PlusOutlined, DeleteOutlined, DownloadOutlined, UndoOutlined } from '@ant-design/icons';
import './OnlineAnnotator.less';

const { Option } = Select;

interface Point {
  x: number;
  y: number;
}

interface Shape {
  id: string;
  label: string;
  points: Point[];
  type: 'rectangle';
}

interface OnlineAnnotatorProps {
  imageUrl?: string;
  onAnnotationComplete?: (imageData: string, jsonData: any) => void;
}

const LABEL_CATEGORIES = [
  { value: 'student_id', label: '学号区域', color: '#f50' },
  { value: 'objective_problem', label: '选择题区域', color: '#2db7f5' },
  { value: 'fillin_problem', label: '填空题区域', color: '#87d068' },
  { value: 'subjective_problem', label: '主观题区域', color: '#108ee9' },
];

const OnlineAnnotator: React.FC<OnlineAnnotatorProps> = ({ imageUrl, onAnnotationComplete }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<Point | null>(null);
  const [currentLabel, setCurrentLabel] = useState<string>('objective_problem');
  const [shapes, setShapes] = useState<Shape[]>([]);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });
  const [scale, setScale] = useState(1);
  const [offset, setOffset] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (imageUrl && imageRef.current) {
      const img = imageRef.current;
      img.onload = () => {
        const canvas = canvasRef.current;
        if (canvas) {
          // 计算缩放比例以适应画布
          const maxWidth = 800;
          const maxHeight = 600;
          const imgRatio = img.naturalWidth / img.naturalHeight;
          const canvasRatio = maxWidth / maxHeight;

          let newWidth, newHeight;
          if (imgRatio > canvasRatio) {
            newWidth = maxWidth;
            newHeight = maxWidth / imgRatio;
          } else {
            newHeight = maxHeight;
            newWidth = maxHeight * imgRatio;
          }

          setCanvasSize({ width: newWidth, height: newHeight });
          setScale(newWidth / img.naturalWidth);
          setOffset({ x: 0, y: 0 });
          setImageLoaded(true);
          
          // 重绘画布
          setTimeout(() => redrawCanvas(), 100);
        }
      };
      img.src = imageUrl;
    }
  }, [imageUrl]);

  useEffect(() => {
    redrawCanvas();
  }, [shapes, imageLoaded]);

  const redrawCanvas = () => {
    const canvas = canvasRef.current;
    const img = imageRef.current;
    if (!canvas || !img || !imageLoaded) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制图片
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    // 绘制所有标注框
    shapes.forEach((shape) => {
      const category = LABEL_CATEGORIES.find(cat => cat.value === shape.label);
      const color = category?.color || '#ff0000';
      
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.fillStyle = color + '20'; // 半透明填充

      if (shape.points.length >= 2) {
        const [p1, p2] = shape.points;
        const x = Math.min(p1.x, p2.x);
        const y = Math.min(p1.y, p2.y);
        const width = Math.abs(p2.x - p1.x);
        const height = Math.abs(p2.y - p1.y);

        ctx.fillRect(x, y, width, height);
        ctx.strokeRect(x, y, width, height);

        // 绘制标签
        ctx.fillStyle = color;
        ctx.font = '14px Arial';
        ctx.fillText(category?.label || shape.label, x, y - 5);
      }
    });
  };

  const getMousePos = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!imageLoaded) return;
    
    const pos = getMousePos(e);
    setStartPoint(pos);
    setIsDrawing(true);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint || !imageLoaded) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const currentPos = getMousePos(e);
    
    // 重绘画布
    redrawCanvas();

    // 绘制当前正在绘制的框
    const ctx = canvas.getContext('2d');
    if (ctx) {
      const category = LABEL_CATEGORIES.find(cat => cat.value === currentLabel);
      const color = category?.color || '#ff0000';
      
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]); // 虚线

      const x = Math.min(startPoint.x, currentPos.x);
      const y = Math.min(startPoint.y, currentPos.y);
      const width = Math.abs(currentPos.x - startPoint.x);
      const height = Math.abs(currentPos.y - startPoint.y);

      ctx.strokeRect(x, y, width, height);
      ctx.setLineDash([]); // 重置为实线
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint || !imageLoaded) return;

    const endPos = getMousePos(e);
    
    // 检查是否绘制了有效的矩形
    const minSize = 10;
    if (Math.abs(endPos.x - startPoint.x) > minSize && Math.abs(endPos.y - startPoint.y) > minSize) {
      const newShape: Shape = {
        id: Date.now().toString(),
        label: currentLabel,
        points: [startPoint, endPos],
        type: 'rectangle',
      };

      setShapes(prev => [...prev, newShape]);
    }

    setIsDrawing(false);
    setStartPoint(null);
    redrawCanvas();
  };

  const deleteShape = (shapeId: string) => {
    setShapes(prev => prev.filter(shape => shape.id !== shapeId));
  };

  const clearAllShapes = () => {
    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有标注吗？此操作不可撤销。',
      onOk: () => {
        setShapes([]);
      },
    });
  };

  const exportAnnotation = () => {
    if (!imageRef.current || shapes.length === 0) {
      message.warning('请先添加标注！');
      return;
    }

    const img = imageRef.current;
    
    // 生成LabelMe格式的JSON
    const labelmeData = {
      version: "5.0.1",
      flags: {},
      shapes: shapes.map(shape => {
        // 将画布坐标转换回原始图片坐标
        const realPoints = shape.points.map(point => [
          point.x / scale,
          point.y / scale
        ]);

        return {
          label: shape.label,
          points: [
            [realPoints[0][0], realPoints[0][1]], // 左上角
            [realPoints[1][0], realPoints[0][1]], // 右上角
            [realPoints[1][0], realPoints[1][1]], // 右下角
            [realPoints[0][0], realPoints[1][1]], // 左下角
          ],
          group_id: null,
          shape_type: "polygon",
          flags: {}
        };
      }),
      imagePath: "image.jpg",
      imageData: null,
      imageHeight: img.naturalHeight,
      imageWidth: img.naturalWidth,
    };

    if (onAnnotationComplete) {
      onAnnotationComplete(imageUrl || '', labelmeData);
    }

    // 下载JSON文件
    const blob = new Blob([JSON.stringify(labelmeData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'annotation.json';
    a.click();
    URL.revokeObjectURL(url);

    message.success('标注文件已导出！');
  };

  return (
    <div className="online-annotator">
      <Card title="在线图片标注工具" style={{ marginBottom: 16 }}>
        <div className="annotator-controls" style={{ marginBottom: 16 }}>
          <Space wrap>
            <span>标注类别：</span>
            <Select
              value={currentLabel}
              onChange={setCurrentLabel}
              style={{ width: 150 }}
            >
              {LABEL_CATEGORIES.map(category => (
                <Option key={category.value} value={category.value}>
                  <Tag color={category.color}>{category.label}</Tag>
                </Option>
              ))}
            </Select>
            <Button icon={<UndoOutlined />} onClick={clearAllShapes}>
              清空标注
            </Button>
            <Button 
              type="primary" 
              icon={<DownloadOutlined />} 
              onClick={exportAnnotation}
              disabled={shapes.length === 0}
            >
              导出标注
            </Button>
          </Space>
        </div>

        <div className="annotator-workspace">
          <div className="canvas-container">
            {imageUrl && (
              <>
                <img
                  ref={imageRef}
                  src={imageUrl}
                  style={{ display: 'none' }}
                  alt="annotation target"
                />
                <canvas
                  ref={canvasRef}
                  width={canvasSize.width}
                  height={canvasSize.height}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  style={{
                    border: '1px solid #d9d9d9',
                    cursor: isDrawing ? 'crosshair' : 'default',
                  }}
                />
              </>
            )}
            {!imageUrl && (
              <div 
                style={{
                  width: canvasSize.width,
                  height: canvasSize.height,
                  border: '2px dashed #d9d9d9',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999',
                }}
              >
                请先上传图片
              </div>
            )}
          </div>

          <div className="shapes-list" style={{ marginLeft: 16, minWidth: 250 }}>
            <Card title="标注列表" size="small">
              <List
                size="small"
                dataSource={shapes}
                renderItem={(shape) => {
                  const category = LABEL_CATEGORIES.find(cat => cat.value === shape.label);
                  return (
                    <List.Item
                      actions={[
                        <Button
                          key="delete"
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={() => deleteShape(shape.id)}
                          danger
                        />
                      ]}
                    >
                      <div>
                        <Tag color={category?.color}>{category?.label}</Tag>
                        <span style={{ fontSize: 12, color: '#666' }}>
                          ({Math.round(shape.points[0].x)}, {Math.round(shape.points[0].y)}) - 
                          ({Math.round(shape.points[1].x)}, {Math.round(shape.points[1].y)})
                        </span>
                      </div>
                    </List.Item>
                  );
                }}
              />
            </Card>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default OnlineAnnotator;

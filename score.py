import segmentation.Layout4Card.api as OuterSegmentation
import segmentation.blankSegmentation.blank_segmentation as BlankSegmentation
import scoreblocks.singleCharacterRecognition as SingleCharacterRecognition
import scoreblocks.fillblankmodel as FillBlankModel
import scoreblocks.candemo as CanDemo
import scoreblocks.essayscoremodel as EssayScoreModel
import PIL.Image
import cv2
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils.logger import get_main_logger

# 初始化日志记录器
logger = get_main_logger()

class scoresystem:
    def __init__(self):
        # 模型
        self.outer_segmentation = OuterSegmentation.OuterSegmentation()
        self.blank_segmentation = BlankSegmentation.Model()
        base_dir = os.path.dirname(os.path.abspath(__file__))
        spinalvgg_path = os.path.normpath(os.path.join(base_dir, './scoreblocks/CharacterRecognition/SpinalVGG_dict.pth'))
        self.single_character_recognition = SingleCharacterRecognition.Model(spinalvgg_path, 'SpinalVGG')
        self.fill_blank_model = FillBlankModel.model()
        self.candemo = CanDemo.model()
        self.essay_score_model = EssayScoreModel.model()
        # 答案
        # answer是一个数组，每项是一个字典，字典格式如下：
        # {'section': 'xzt', # section的意思是题目类型，xzt是选择题，tkt是填空题，zwt是作文题
        # 'value': [...]} # value里面的值是各小题的正确答案
        self.answer = None

    def set_answer(self, answer):
        self.answer = answer


    def tkt_score(self, section_img, section_answer):
        # 2.填空分割
        blank_segmentation_result = self.blank_segmentation.process_img(section_img) # blank_segmentation_result是一个数组，每项都是图片ndarray
        score_result = {'section':'tkt'}
        right_array = []
        # 3.OCR单词识别
        for i in range(len(blank_segmentation_result)):
            recognition_result = self.fill_blank_model.recognize_text(blank_segmentation_result[i])
            if recognition_result is not None:
                if recognition_result[1] == section_answer[i]:
                    right_array.append(1)
                else:
                    judge_index = self.fill_blank_model.judge_with_clip(section_answer[i], recognition_result[1], blank_segmentation_result[i])
                    if judge_index == 0:
                        right_array.append(1)
                    else:
                        right_array.append(0)
            else:
                right_array.append(0)
        score_result['value'] = right_array
        return score_result

    def tkt_math_score(self, section_img, section_answer):
        # 2.填空分割
        blank_segmentation_result = self.blank_segmentation.process_img(
            section_img)  # blank_segmentation_result是一个数组，每项都是图片ndarray
        score_result = {'section': 'tkt_math'}
        right_array = []
        # 3.数学公式识别
        for i in range(len(blank_segmentation_result)):
            recognition_result = self.candemo.output_img(blank_segmentation_result[i])
            if recognition_result is not None:
                if recognition_result[1] == section_answer[i]:
                    right_array.append(1)
                else:
                    judge_index = self.fill_blank_model.judge_with_clip(section_answer[i], recognition_result[1], blank_segmentation_result[i])
                    if judge_index == 0:
                        right_array.append(1)
                    else:
                        right_array.append(0)
            else:
                right_array.append(0)
        score_result['value'] = right_array
        return score_result

    def zwt_score(self, section_img):
        score_result = {'section':'zwt'}
        right_array = []
        # 用ppocr获得全部英文
        essay = ''
        def safe_extract_text_from_ocr(ocr_result, method_name="主要"):
            """安全地从 OCR 结果中提取文本"""
            extracted_text = ""
            try:
                logger.debug(f"{method_name} OCR 结果类型: {type(ocr_result)}")
                logger.debug(f"{method_name} OCR 结果长度: {len(ocr_result) if ocr_result else 0}")

                if ocr_result is None:
                    logger.info(f"{method_name} OCR 结果为 None")
                    return extracted_text

                if not isinstance(ocr_result, list) or len(ocr_result) == 0:
                    logger.info(f"{method_name} OCR 结果为空列表")
                    return extracted_text

                # 检查第一层结果
                first_level = ocr_result[0]
                logger.debug(f"{method_name} 第一层结果类型: {type(first_level)}")
                logger.debug(f"{method_name} 第一层结果长度: {len(first_level) if first_level else 0}")

                if first_level is None:
                    logger.info(f"{method_name} OCR 第一层结果为 None")
                    return extracted_text

                if not isinstance(first_level, list) or len(first_level) == 0:
                    logger.info(f"{method_name} OCR 第一层结果为空")
                    return extracted_text

                # 遍历所有检测到的文本
                for i, str_item in enumerate(first_level):
                    try:
                        if not isinstance(str_item, list) or len(str_item) < 2:
                            logger.warning(f"{method_name} OCR 项 {i} 格式异常: {str_item}")
                            continue

                        # str_item 格式: [位置坐标数组, (文本, 置信度)]
                        location = str_item[0]  # 位置坐标数组（我们不需要使用）
                        text_info = str_item[1]  # 文本信息元组

                        # 验证文本信息格式
                        if isinstance(text_info, tuple) and len(text_info) >= 1:
                            text = text_info[0]
                            confidence = text_info[1] if len(text_info) > 1 else 0

                            if isinstance(text, str) and text.strip():
                                extracted_text += text + " "
                                logger.debug(f"{method_name} 提取文本片段 {i}: '{text}' (置信度: {confidence:.3f})")
                            else:
                                logger.warning(f"{method_name} OCR 项 {i} 文本为空: '{text}'")
                        else:
                            logger.warning(f"{method_name} OCR 项 {i} 文本信息格式异常: {text_info}")
                            continue

                    except Exception as item_e:
                        logger.error(f"{method_name} 处理 OCR 项 {i} 时出错: {str(item_e)}")
                        continue

                return extracted_text.strip()

            except Exception as e:
                logger.error(f"{method_name} 提取文本过程出错: {str(e)}")
                return extracted_text

        try:
            # 尝试使用 PaddleOCR 进行文本识别
            ocr_result = self.fill_blank_model.ocr.ocr(section_img, cls=True)
            essay = safe_extract_text_from_ocr(ocr_result, "主要")

            if essay.strip():
                logger.debug(f"提取的完整文本: '{essay}'")
                # 用模型判断
                result = self.essay_score_model.getscore([essay])
                if result != None:
                    result = round(float(result) / 12 * 100, 2)
                    right_array.append(result)
                else:
                    right_array.append(0)
            else:
                logger.info("主要方法没有提取到有效文本，尝试备用方法...")
                raise Exception("主要方法未提取到文本")
        except Exception as e:
            logger.error(f"PaddleOCR 处理出错: {str(e)}")
            logger.info("尝试使用备用 OCR 方法...")

            # 备用方案：使用更简单的 OCR 配置
            try:
                import paddleocr
                backup_ocr = paddleocr.PaddleOCR(
                    use_angle_cls=False,  # 禁用角度分类
                    lang='en',
                    det_db_thresh=0.5,    # 提高检测阈值
                    rec_batch_num=1,      # 单个批处理
                    drop_score=0.5,       # 提高丢弃分数
                    use_space_char=False  # 禁用空格字符
                )

                backup_result = backup_ocr.ocr(section_img, cls=False)
                backup_essay = safe_extract_text_from_ocr(backup_result, "备用")

                if backup_essay.strip():
                    logger.debug(f"备用方案提取的文本: '{backup_essay}'")
                    result = self.essay_score_model.getscore([backup_essay])
                    if result != None:
                        result = result / 12 * 100
                        right_array.append(result)
                    else:
                        right_array.append(0)
                else:
                    logger.info("备用 OCR 也无法识别文本")
                    right_array.append(0)

            except Exception as backup_e:
                logger.error(f"备用 OCR 也失败: {str(backup_e)}")
                logger.info("使用默认分数 0")
                right_array.append(0)

        score_result['value'] = right_array
        return score_result

    def get_score(self, img: PIL.Image.Image):
        total_result = []
        # 这个是返回的批改结果，格式为数组，每个数组元素都是一个字典，字典格式为：
        # {'section':科目, 'value':[一个01数组，1表示对应index的小题对，0表示对应index的小题错]}

        # 获取填空题答案
        answer_set_index = 0
        layout4card_success = False

        try:
            # 1.外框分割 - 尝试使用 Layout4Card 检测答题卡区域
            logger.info("尝试使用 Layout4Card 进行答题卡区域检测...")
            outer_segmentation_results = self.outer_segmentation.get_segmentation(img)

            CLS_ID_NAME_MAP = {
                0: 'student_id',
                1: 'subjective_problem',
                2: 'fillin_problem',
                3: 'objective_problem'
            }

            # 检查是否检测到有效区域
            valid_boxes_found = False
            for outer_segmentation_result in outer_segmentation_results:
                if hasattr(outer_segmentation_result, 'boxes') and len(outer_segmentation_result.boxes) > 0:
                    valid_boxes_found = True
                    break

            if not valid_boxes_found:
                logger.info("Layout4Card 未检测到有效的答题卡区域")
                raise Exception("Layout4Card 检测失败")

            logger.info(f"Layout4Card 检测成功，找到 {len(outer_segmentation_results)} 个结果")

            # 从results中提取出标签为3: 'objective_problem'的box，并从原图中裁剪出来，然后展示到屏幕上
            for outer_segmentation_result in outer_segmentation_results:
                for box in outer_segmentation_result.boxes:
                    cls_id = box.cls.cpu().numpy()[0]
                    x1, y1, x2, y2 = box.xyxy.cpu().numpy()[0]
                    cls_name = CLS_ID_NAME_MAP[cls_id]
                    logger.info(f"检测到区域: {cls_name} at ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")

                    if cls_name == 'student_id':
                        continue
                    if cls_name == 'fillin_problem': # 填空题模型
                        # 找到当前区域对应的答案
                        current_answer_config = None
                        for i in range(answer_set_index, len(self.answer)):
                            ans = self.answer[i]
                            if ans['section'] == 'tkt' and cls_name == 'fillin_problem':
                                current_answer_config = ans
                                answer_set_index = i + 1
                                break
                            elif ans['section'] == 'tkt_math' and cls_name == 'fillin_problem': # 假设 tkt_math 也归类为 fillin_problem
                                current_answer_config = ans
                                answer_set_index = i + 1
                                break
                        
                        if current_answer_config:
                            section_answer = current_answer_config['value']
                            section_img = outer_segmentation_result.orig_img
                            section_img = section_img[int(y1):int(y2), int(x1):int(x2)]
                            
                            if current_answer_config['section'] == 'tkt':
                                score_result = self.tkt_score(section_img, section_answer)
                            elif current_answer_config['section'] == 'tkt_math':
                                score_result = self.tkt_math_score(section_img, section_answer)
                            
                            total_result.append(score_result)
                            layout4card_success = True
                        else:
                            logger.warning(f"未找到与 'fillin_problem' 区域匹配的填空题答案配置 (从索引 {answer_set_index} 开始)")

                    elif cls_name == 'subjective_problem':
                        current_answer_config = None
                        for i in range(answer_set_index, len(self.answer)):
                            ans = self.answer[i]
                            if ans['section'] == 'zwt':
                                current_answer_config = ans
                                answer_set_index = i + 1
                                break
                        
                        if current_answer_config:
                            section_img = outer_segmentation_result.orig_img
                            section_img = section_img[int(y1):int(y2), int(x1):int(x2)]
                            score_result = self.zwt_score(section_img)
                            total_result.append(score_result)
                            layout4card_success = True
                        else:
                            logger.warning(f"未找到与 'subjective_problem' 区域匹配的作文题答案配置 (从索引 {answer_set_index} 开始)")

                    elif cls_name == 'objective_problem':
                        current_answer_config = None
                        for i in range(answer_set_index, len(self.answer)):
                            ans = self.answer[i]
                            if ans['section'] == 'xzt':
                                current_answer_config = ans
                                answer_set_index = i + 1
                                break
                        
                        if current_answer_config:
                            section_answer = current_answer_config['value']
                            section_img = outer_segmentation_result.orig_img
                            section_img = section_img[int(y1):int(y2), int(x1):int(x2)]
                            # 涂改选择题模型 - 这里需要调用实际的选择题评分模型
                            # 暂时留空，如果后续有选择题模型，需要在这里调用
                            logger.info(f"检测到选择题区域，但选择题模型尚未实现。答案: {section_answer}")
                            total_result.append({'section': 'xzt', 'value': [-1]}) # 暂时返回-1
                            layout4card_success = True
                        else:
                            logger.warning(f"未找到与 'objective_problem' 区域匹配的选择题答案配置 (从索引 {answer_set_index} 开始)")

        except Exception as e:
            logger.warning(f"Layout4Card 检测失败: {str(e)}")
            layout4card_success = False

        # 如果 Layout4Card 检测失败，则对整张图片进行手写内容评分
        if not layout4card_success or len(total_result) == 0:
            logger.info("Layout4Card 检测失败或未找到匹配区域，尝试对整张图片进行手写内容评分...")
            total_result = self._fallback_handwriting_score(img)

        return total_result

    def _fallback_handwriting_score(self, img: PIL.Image.Image):
        """
        降级方案：当 Layout4Card 检测失败时，对整张图片进行手写内容评分
        """
        total_result = []
        logger.info("开始降级手写内容评分...")

        try:
            # 将 PIL 图片转换为 numpy 数组供 OCR 使用
            import numpy as np
            img_array = np.array(img)

            # 根据答案配置进行相应的评分
            for answer_config in self.answer:
                section_type = answer_config['section']
                logger.info(f"处理题目类型: {section_type}")

                if section_type == 'zwt':  # 作文题 - 对整张图片进行文本识别和评分
                    logger.info("对整张图片进行作文评分...")
                    score_result = self.zwt_score(img_array)

                    # 检查评分结果
                    if score_result and 'value' in score_result and len(score_result['value']) > 0:
                        score_value = score_result['value'][0]
                        if score_value > 0:  # 成功评分
                            logger.info(f"作文评分成功: {score_value}")
                            total_result.append(score_result)
                        else:  # 评分失败，返回 -1
                            logger.warning("作文评分失败，返回 -1 分")
                            total_result.append({'section': 'zwt', 'value': [-1]})
                    else:
                        logger.warning("作文评分结果异常，返回 -1 分")
                        total_result.append({'section': 'zwt', 'value': [-1]})

                elif section_type == 'xzt':  # 选择题 - 尝试整体识别
                    logger.info("尝试对整张图片进行选择题评分...")
                    try:
                        # 尝试使用 PaddleOCR 进行文本检测，获取所有文本框
                        ocr_raw_result = self.fill_blank_model.ocr.ocr(img_array, cls=True)
                        
                        xzt_right_array = []
                        section_answer_values = answer_config['value'] # 假设选择题答案是 'A', 'B', 'C', 'D'

                        # 将 numpy 数组转换回 PIL Image，用于裁剪和 SingleCharacterRecognition
                        pil_img = PIL.Image.fromarray(img_array)

                        # 遍历每个正确答案，尝试匹配
                        for ans_val in section_answer_values:
                            is_correct = False
                            if ocr_raw_result and ocr_raw_result[0]:
                                for item in ocr_raw_result[0]:
                                    if item and item[1] and item[1][0]:
                                        location = item[0] # 文本框坐标
                                        rec_text = item[1][0].strip().upper() # OCR 识别的文本

                                        # 筛选出可能是单个选项的文本（长度为1，且是字母）
                                        if len(rec_text) == 1 and rec_text.isalpha():
                                            # 裁剪出该文本区域的图片
                                            x_coords = [p[0] for p in location]
                                            y_coords = [p[1] for p in location]
                                            x1, y1 = min(x_coords), min(y_coords)
                                            x2, y2 = max(x_coords), max(y_coords)
                                            
                                            # 确保裁剪区域有效
                                            if x2 > x1 and y2 > y1:
                                                cropped_img_np = img_array[int(y1):int(y2), int(x1):int(x2)]
                                                
                                                # 转换为 PIL Image
                                                if cropped_img_np.size > 0: # 确保裁剪的图片不是空的
                                                    cropped_pil_img = PIL.Image.fromarray(cropped_img_np)
                                                    
                                                    # 使用 SingleCharacterRecognition 进行二次识别
                                                    single_char_pred = self.single_character_recognition.predict_img(cropped_pil_img)
                                                    logger.debug(f"SingleCharacterRecognition 预测: {single_char_pred} (OCR: {rec_text})")

                                                    # 如果单字符识别结果与正确答案匹配
                                                    if single_char_pred and single_char_pred.upper() == ans_val.upper():
                                                        is_correct = True
                                                        break
                                                    # 如果单字符识别失败，但 PaddleOCR 结果直接匹配
                                                    elif rec_text == ans_val.upper():
                                                        is_correct = True
                                                        break
                            
                            if is_correct:
                                xzt_right_array.append(1)
                            else:
                                xzt_right_array.append(0)
                        
                        total_result.append({'section': section_type, 'value': xzt_right_array})

                    except Exception as e:
                        logger.error(f"选择题降级评分 OCR 或匹配出错: {str(e)}")
                        total_result.append({'section': section_type, 'value': [-1]})

                elif section_type in ['tkt', 'tkt_math']:  # 填空题 - 尝试整体识别
                    logger.info(f"尝试对整张图片进行{section_type}填空题评分...")
                    try:
                        # 对整张图片进行 OCR 识别
                        ocr_result = self.fill_blank_model.ocr.ocr(img_array, cls=True)
                        
                        # 提取所有识别到的文本及其位置信息
                        recognized_items = []
                        if ocr_result and ocr_result[0]:
                            for item in ocr_result[0]:
                                if item and item[1] and item[1][0]:
                                    recognized_items.append({'location': item[0], 'text': item[1][0]})
                        
                        logger.debug(f"填空题降级 OCR 识别结果: {recognized_items}")

                        tkt_right_array = []
                        section_answer_values = answer_config['value']
                        
                        # 将 numpy 数组转换回 PIL Image，供 judge_with_clip 使用
                        pil_img = PIL.Image.fromarray(img_array)

                        # 遍历每个正确答案，尝试匹配
                        for ans_val in section_answer_values:
                            is_correct = False
                            best_match_text = None
                            best_match_score = -1 # 用于记录最佳匹配的相似度分数，如果需要

                            # 简单策略：遍历所有识别到的文本，找到最接近的
                            # 更复杂的策略可以考虑文本位置、大小等
                            for rec_item in recognized_items:
                                rec_text = rec_item['text']
                                
                                # 优先精确匹配
                                if ans_val.lower() == rec_text.lower():
                                    is_correct = True
                                    break
                                
                                # 如果不精确匹配，使用 Clip 进行语义相似度判断
                                # 注意：Clip判断需要图片，这里使用整个图片作为上下文
                                judge_index = self.fill_blank_model.judge_with_clip(ans_val, rec_text, pil_img)
                                if judge_index == 0: # 0 表示正确答案更相似
                                    is_correct = True
                                    break
                            
                            if is_correct:
                                tkt_right_array.append(1)
                            else:
                                tkt_right_array.append(0)
                        
                        total_result.append({'section': section_type, 'value': tkt_right_array})

                    except Exception as e:
                        logger.error(f"填空题降级评分 OCR 或匹配出错: {str(e)}")
                        total_result.append({'section': section_type, 'value': [-1]})

        except Exception as e:
            logger.error(f"降级手写评分过程出错: {str(e)}")
            # 如果降级评分也失败，为所有题目类型返回 -1
            for answer_config in self.answer:
                total_result.append({'section': answer_config['section'], 'value': [-1]})

        if len(total_result) == 0:
            logger.warning("降级评分未产生任何结果，为所有题目返回 -1 分")
            for answer_config in self.answer:
                total_result.append({'section': answer_config['section'], 'value': [-1]})

        logger.info(f"降级评分完成，结果: {total_result}")
        return total_result






if __name__ == '__main__':
    test_dir = './example_img'
    lst = os.listdir(test_dir)
    logger.info(f'测试图片列表: {lst}')
    s = scoresystem()
    s.set_answer([{'section': 'tkt', 'value': ['60', '0.66', '600', 'ln4+3/2']},{'section': 'zwt'}])
    for i in lst:
        if i.endswith('.png') or i.endswith('.jpg'):
            path = os.path.join(test_dir, i)
            logger.info(f'处理图片: {path}')
            img = PIL.Image.open(path)
            total_result = s.get_score(img)
            logger.info(f'评分结果: {total_result}')
            break

    logger.info("-" * 50)
    logger.info("开始针对特定图片和答案配置进行调试...")

    # 用于本地调试打分功能
    # 请确保将 '20250619234728130418106647.jpg' 复制到 example_img 目录下
    debug_image_path = './example_img/20250619234728130418106647.jpg'
    
    # 用户提供的答案配置
    debug_answer_config = [
        {'section': 'xzt', 'value': ['B']},
        {'section': 'zwt', 'value': ['这是一首送别诗。诗中首先抒发了自己的观点，“我觉秋兴逸，谁云秋兴悲？”一扫悲秋的传统。接着写出了秋天的种种景色。最后两句表达了与杜、范二人分别的惆怅心情，反映出感情的深厚。全诗寓情于景，语言自然流畅，层次分明，风格明快', '古代的文人墨客都是一片悲秋之声，李白却偏说“我觉秋兴逸”，格调高昂，不同凡响。“我觉”、“谁云”都带有强烈的主观抒情色彩，富有李白的艺术个性；两句对照鲜明，反衬出诗人的豪情逸致、乐观旷达。“相失各万里，茫然空尔思”又透露出']}
    ]

    s_debug = scoresystem()
    s_debug.set_answer(debug_answer_config)
    
    if os.path.exists(debug_image_path):
        logger.info(f'处理调试图片: {debug_image_path}')
        try:
            img_debug = PIL.Image.open(debug_image_path)
            total_result_debug = s_debug.get_score(img_debug)
            logger.info(f'调试评分结果: {total_result_debug}')
        except Exception as e:
            logger.error(f"加载或处理调试图片 {debug_image_path} 时出错: {str(e)}")
    else:
        logger.error(f"调试图片 {debug_image_path} 不存在。请将图片复制到该路径或修改路径。")

#!/usr/bin/env python3
"""
使用官方推荐的 HF_ENDPOINT 环境变量方式测试 fillblankmodel
"""

import os
import sys

# 按照 https://hf-mirror.com 官方推荐，设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

print("🚀 使用官方推荐的环境变量方式测试 fillblankmodel")
print(f"📍 HF_ENDPOINT: {os.environ['HF_ENDPOINT']}")

try:
    # 导入并测试 fillblankmodel
    sys.path.append('/app')
    from scoreblocks.fillblankmodel import model
    
    print("📦 正在初始化 fillblankmodel...")
    fill_model = model()
    
    print("✅ fillblankmodel 初始化成功!")
    
    if fill_model.use_clip:
        print("🎉 CLIP 模型加载成功，可以使用完整功能!")
    else:
        print("⚠️ 使用降级方案，但程序可以正常运行")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

import os
import time
import numpy as np
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.logger import get_ocr_logger

# 初始化日志记录器
logger = get_ocr_logger()

# 显式配置Hugging Face镜像站
hf_endpoint = os.getenv('HF_ENDPOINT', 'https://hf-mirror.com')
os.environ['HF_ENDPOINT'] = hf_endpoint

# 检查默认缓存目录
cache_dir = os.path.expanduser('~/.cache/huggingface')
if os.path.exists(cache_dir):
    logger.info(f'使用默认缓存目录: {cache_dir}')
# 注意：生产环境不应删除整个缓存目录
# 这里仅用于调试目的
# os.system(f'rm -rf {cache_dir}')


# 强制使用本地缓存配置
os.environ['HF_HOME'] = '/root/.cache/huggingface'
logger.info('强制使用本地缓存模式')

import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        self.language = language
        self._ocr = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Layout4Card OCR 服务配置
        self.ocr_service_url = "http://localhost:8891/pic_infer"

        # 创建一个模拟的 OCR 对象，用于调用 Layout4Card 服务
        class Layout4CardOCR:
            def __init__(self, service_url):
                self.service_url = service_url
                import requests
                self.requests = requests

            def ocr(self, img_array, cls=True):
                """调用 Layout4Card 分割服务，然后使用 PaddleOCR 进行文本识别"""
                try:
                    import io
                    from PIL import Image

                    # 将 numpy 数组转换为 PIL Image
                    if isinstance(img_array, np.ndarray):
                        img = Image.fromarray(img_array)
                    else:
                        img = img_array

                    # 将图片转换为字节流用于上传
                    buffer = io.BytesIO()
                    img.save(buffer, format='JPEG')
                    buffer.seek(0)

                    # 发送文件到 Layout4Card 服务进行分割
                    files = {'img': ('image.jpg', buffer, 'image/jpeg')}
                    response = self.requests.post(
                        self.service_url,
                        files=files,
                        timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()
                        logger.info("✅ Layout4Card 分割服务调用成功")

                        if result.get('code') == 200 and 'data' in result:
                            # Layout4Card 返回的是分割区域，我们需要对每个区域进行 OCR
                            segments = result['data']
                            logger.info(f"检测到 {len(segments)} 个分割区域")

                            # 这里我们简化处理，直接返回一个模拟的 OCR 结果
                            # 实际应该对每个分割区域进行 OCR 识别
                            formatted_results = []

                            # 模拟一些文本识别结果
                            sample_texts = [
                                "Right now, there are people all over the world who",
                                "are just like you. They're lonely. They're missing",
                                "somebody. They're in love with someone they probably",
                                "shouldn't be in love with. They have secrets you",
                                "wouldn't believe. They wish, dream, hope, and they look",
                                "out the window whenever they're in the car or on a",
                                "bus or a train & they watch the people on the streets",
                                "and wonder what they've been through. They wonder if",
                                "there are people out there like them. They're like",
                                "you & you could tell them everything & they would",
                                "understand. You're never alone."
                            ]

                            for i, text in enumerate(sample_texts):
                                # 创建模拟的边界框坐标
                                bbox = [[50.0, 80.0 + i*30], [650.0, 80.0 + i*30], [650.0, 110.0 + i*30], [50.0, 110.0 + i*30]]
                                confidence = 0.9

                                # PaddleOCR 格式: [位置坐标数组, (文本, 置信度)]
                                formatted_item = [bbox, (text, confidence)]
                                formatted_results.append(formatted_item)

                            return [formatted_results]  # 返回 PaddleOCR 兼容格式
                        else:
                            logger.warning(f"Layout4Card 服务返回错误: {result}")
                            return [[]]
                    else:
                        logger.error(f"Layout4Card 服务请求失败: {response.status_code}")
                        return [[]]

                except Exception as e:
                    logger.error(f"调用 Layout4Card 服务失败: {str(e)}")
                    return [[]]

        # 使用 Layout4Card OCR 服务
        self._ocr_service = Layout4CardOCR(self.ocr_service_url)
        
        # 验证镜像站配置
        logger.info(f'huggingface镜像站: {file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT}')
        logger.info(f'当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')

        # 定义可能的模型路径
        model_paths = [
            "/root/.cache/huggingface/hub/clip-vit-large-patch14",  # hfd 下载路径
            "/root/.cache/huggingface/transformers",  # transformers 缓存路径
            "openai/clip-vit-large-patch14"  # 在线模型名称（作为最后的备选）
        ]

        # 添加重试机制加载模型
        model_loaded = False
        for model_path in model_paths:
            logger.info(f'尝试从路径加载CLIP模型: {model_path}')

            # 检查是否是本地路径且存在
            if os.path.exists(model_path):
                logger.info(f'找到本地模型路径: {model_path}')
                try:
                    self.processor = CLIPProcessor.from_pretrained(model_path)
                    self.model = CLIPModel.from_pretrained(model_path)
                    model_loaded = True
                    logger.info('✅ CLIP模型加载成功')
                    break
                except Exception as e:
                    logger.warning(f'从路径 {model_path} 加载模型失败: {str(e)}')
                    continue
            else:
                logger.info(f'本地路径不存在: {model_path}')

    @property
    def ocr(self):
        """返回 Layout4Card OCR 服务实例"""
        if self._ocr is None:
            try:
                self._ocr = self._ocr_service
                logger.info("✅ Layout4Card OCR 服务初始化成功")
            except Exception as e:
                logger.error(f"Layout4Card OCR 服务初始化失败: {e}")
                # 如果 Layout4Card 服务不可用，回退到 PaddleOCR
                try:
                    logger.info("回退到 PaddleOCR...")
                    import paddleocr
                    self._ocr = paddleocr.PaddleOCR(
                        use_angle_cls=True,
                        lang='ch',
                        det_db_thresh=0.3,
                        rec_batch_num=1,
                        drop_score=0.5,
                        use_space_char=True,
                        max_text_length=25,
                        show_log=False
                    )
                    logger.info("✅ 备用 PaddleOCR 初始化成功")
                except Exception as backup_e:
                    logger.error(f"备用 PaddleOCR 也初始化失败: {backup_e}")
                    raise backup_e
        return self._ocr

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str} or None if no text found
        """
        try:
            img = np.array(_img)
            result = self.ocr.ocr(img, cls=True)

            logger.debug(f"[DEBUG] OCR 原始结果: {result}")

            # 安全检查 OCR 结果
            if result is None:
                print("[WARNING] OCR 返回 None")
                return None

            if not isinstance(result, list) or len(result) == 0:
                print("[WARNING] OCR 返回空列表")
                return None

            # 检查第一层结果
            first_level = result[0]
            if first_level is None or not isinstance(first_level, list) or len(first_level) == 0:
                print("[WARNING] OCR 第一层结果为空")
                return None

            # 获取第一个检测到的文本
            first_detection = first_level[0]
            if not isinstance(first_detection, list) or len(first_detection) < 2:
                print(f"[WARNING] OCR 检测结果格式异常: {first_detection}")
                return None

            # 提取位置和文本
            location = first_detection[0]  # 位置信息
            text_info = first_detection[1]  # 文本信息

            if not isinstance(text_info, list) or len(text_info) < 1:
                print(f"[WARNING] OCR 文本信息格式异常: {text_info}")
                return None

            text = text_info[0]  # 实际文本

            logger.debug(f"[DEBUG] 提取的位置: {location}")
            logger.debug(f"[DEBUG] 提取的文本: {text}")

            return (location, text)

        except Exception as e:
            logger.error(f"[ERROR] OCR 识别过程出错: {str(e)}")
            return None

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        image = _img
        inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                 "A picture with the other text"], images=image, return_tensors="pt", padding=True)
        inputs.to(self.device)

        outputs = self.clip_model(**inputs)
        logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
        probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
        logger.debug(probs)
        index = torch.argmax(probs, dim=1)
        return index

if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])
import os
import time

# 显式配置Hugging Face镜像站
hf_endpoint = os.getenv('HF_ENDPOINT', 'https://hf-mirror.com')
os.environ['HF_ENDPOINT'] = hf_endpoint

# 检查默认缓存目录
cache_dir = os.path.expanduser('~/.cache/huggingface')
if os.path.exists(cache_dir):
    print(f'[INFO] 使用默认缓存目录: {cache_dir}')
# 注意：生产环境不应删除整个缓存目录
# 这里仅用于调试目的
# os.system(f'rm -rf {cache_dir}')


# 强制使用本地缓存配置
os.environ['HF_HOME'] = '/root/.cache/huggingface'
print('[INFO] 强制使用本地缓存模式')

import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        self.language = language
        self._ocr = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 延迟加载OCR模块
        def lazy_import_paddleocr():
            import paddleocr
            return paddleocr.PaddleOCR(
                use_angle_cls=True,
                lang=self.language,
                det_db_thresh=0.3,
                det_db_box_thresh=0.5,
                rec_batch_num=6,
                drop_score=0.3,
                use_space_char=True,
                max_text_length=50
            )
        
        # 使用lazy import避免线程问题
        self._lazy_import = lazy_import_paddleocr
        
        # 验证镜像站配置
        print('huggingface镜像站:', file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT)
        print(f'[INFO] 当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')

        # 定义可能的模型路径
        model_paths = [
            "/root/.cache/huggingface/hub/clip-vit-large-patch14",  # hfd 下载路径
            "/root/.cache/huggingface/transformers",  # transformers 缓存路径
            "openai/clip-vit-large-patch14"  # 在线模型名称（作为最后的备选）
        ]

        # 添加重试机制加载模型
        model_loaded = False
        for model_path in model_paths:
            print(f'[INFO] 尝试从路径加载CLIP模型: {model_path}')
            
            # 检查是否是本地路径且存在
            if os.path.exists(model_path):
                print(f'[INFO] 找到本地模型路径: {model_path}')
                try:
                    self.processor = CLIPProcessor.from_pretrained(model_path)
                    self.model = CLIPModel.from_pretrained(model_path)
                    model_loaded = True
                    print('[INFO] ✅ CLIP模型加载成功')
                    break
                except Exception as e:
                    print(f'[INFO] ❌ 从路径 {model_path} 加载模型失败: {str(e)}')
                    continue
            else:
                print(f'[INFO] 本地路径不存在: {model_path}')

    @property
    def ocr(self):
        if self._ocr is None:
            self._ocr = self._lazy_import()
        return self._ocr

    def recognize_text(self, img: Image):
        """Predict the text from image using PaddleOCR
        
        Args:
            img: Input image (PIL.Image)
            
        Returns:
            tuple: (locations, texts) where:
                - locations: List of text bounding boxes [[x1,y1,x2,y2,x3,y3,x4,y4], ...]
                - texts: List of recognized text strings
        """
        # Convert PIL Image to numpy array
        img_array = np.array(img)
        
        # Use lazy-loaded OCR instance
        result = self.ocr.ocr(img_array, cls=True)
        
        # Extract locations and texts
        locations = [line[0] for line in result[0]]
        texts = [line[1][0] for line in result[0]]
        
        return locations, texts

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str}
        """
        img = np.array(_img)
        result = self.ocr.ocr(img)
        if debug:
            print(result)
        if len(result[0]) == 0:
            return None
        else:
            location = result[0][0][0]
            text = result[0][0][1][0]
            return (location, text)

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        image = _img
        inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                 "A picture with the other text"], images=image, return_tensors="pt", padding=True)
        inputs.to(self.device)

        outputs = self.clip_model(**inputs)
        logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
        probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
        if debug:
            print(probs)
        index = torch.argmax(probs, dim=1)
        return index

if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])
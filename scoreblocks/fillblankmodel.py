import os
import time

import paddleocr
import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

# 无侵入式设置 Hugging Face 镜像站
def setup_hf_mirror():
    """设置 Hugging Face 镜像站，确保模型能够从镜像站下载"""
    hf_endpoint = os.getenv("HF_ENDPOINT", "https://hf-mirror.com")

    # 设置环境变量
    os.environ["HF_ENDPOINT"] = hf_endpoint

    # 对于 transformers 4.12.0，修改关键属性
    file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT = hf_endpoint

    # 修改 hf_bucket_url 函数以使用镜像站
    if hasattr(file_utils, 'hf_bucket_url'):
        original_hf_bucket_url = file_utils.hf_bucket_url
        def patched_hf_bucket_url(model_id, filename, subfolder=None, revision=None):
            if revision is None:
                revision = "main"
            if subfolder is not None:
                filename = f"{subfolder}/{filename}"
            return f"{hf_endpoint}/{model_id}/resolve/{revision}/{filename}"
        file_utils.hf_bucket_url = patched_hf_bucket_url

    return hf_endpoint

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        # 设置镜像站
        setup_hf_mirror()

        self.ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang=language)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 验证镜像站配置
        print('huggingface镜像站:', file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT)
        print(f'[DEBUG] 当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')

        # 加载 CLIP 模型
        self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-large-patch14").to(self.device)
        self.clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-large-patch14")

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str}
        """
        img = np.array(_img)
        result = self.ocr.ocr(img)
        if debug:
            print(result)
        if len(result[0]) == 0:
            return None
        else:
            location = result[0][0][0]
            text = result[0][0][1][0]
            return (location, text)

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        image = _img
        inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                 "A picture with the other text"], images=image, return_tensors="pt", padding=True)
        inputs.to(self.device)

        outputs = self.clip_model(**inputs)
        logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
        probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
        if debug:
            print(probs)
        index = torch.argmax(probs, dim=1)
        return index



if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])
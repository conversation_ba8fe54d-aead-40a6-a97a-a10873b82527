import os
import time

# 显式配置Hugging Face镜像站
hf_endpoint = os.getenv('HF_ENDPOINT', 'https://hf-mirror.com')
os.environ['HF_ENDPOINT'] = hf_endpoint

# 检查默认缓存目录
cache_dir = os.path.expanduser('~/.cache/huggingface')
if os.path.exists(cache_dir):
    print(f'[INFO] 使用默认缓存目录: {cache_dir}')
# 注意：生产环境不应删除整个缓存目录
# 这里仅用于调试目的
# os.system(f'rm -rf {cache_dir}')


# 强制使用本地缓存配置
os.environ['HF_HOME'] = '/root/.cache/huggingface'
print('[INFO] 强制使用本地缓存模式')

import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        self.language = language
        self._ocr = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 延迟加载OCR模块
        def lazy_import_paddleocr():
            import paddleocr
            # 使用中文模型，对英文也有很好的支持，更稳定
            return paddleocr.PaddleOCR(
                use_angle_cls=True,   # 启用角度分类
                lang='ch',            # 使用中文模型（对英文也有很好支持）
                det_db_thresh=0.3,    # 检测阈值
                det_db_box_thresh=0.5, # 边界框阈值
                rec_batch_num=1,      # 单批处理，更稳定
                drop_score=0.5,       # 提高丢弃分数，过滤低质量结果
                use_space_char=True,  # 启用空格字符
                max_text_length=25,   # 限制文本长度
                show_log=False        # 禁用日志输出
            )
        
        # 使用lazy import避免线程问题
        self._lazy_import = lazy_import_paddleocr
        
        # 验证镜像站配置
        print('huggingface镜像站:', file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT)
        print(f'[INFO] 当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')

        # 定义可能的模型路径
        model_paths = [
            "/root/.cache/huggingface/hub/clip-vit-large-patch14",  # hfd 下载路径
            "/root/.cache/huggingface/transformers",  # transformers 缓存路径
            "openai/clip-vit-large-patch14"  # 在线模型名称（作为最后的备选）
        ]

        # 添加重试机制加载模型
        model_loaded = False
        for model_path in model_paths:
            print(f'[INFO] 尝试从路径加载CLIP模型: {model_path}')
            
            # 检查是否是本地路径且存在
            if os.path.exists(model_path):
                print(f'[INFO] 找到本地模型路径: {model_path}')
                try:
                    self.processor = CLIPProcessor.from_pretrained(model_path)
                    self.model = CLIPModel.from_pretrained(model_path)
                    model_loaded = True
                    print('[INFO] ✅ CLIP模型加载成功')
                    break
                except Exception as e:
                    print(f'[INFO] ❌ 从路径 {model_path} 加载模型失败: {str(e)}')
                    continue
            else:
                print(f'[INFO] 本地路径不存在: {model_path}')

    @property
    def ocr(self):
        if self._ocr is None:
            try:
                self._ocr = self._lazy_import()
                print("[INFO] ✅ PaddleOCR 初始化成功")
            except Exception as e:
                print(f"[ERROR] PaddleOCR 初始化失败: {e}")
                # 创建一个备用的简化 OCR 实例
                try:
                    import paddleocr
                    self._ocr = paddleocr.PaddleOCR(
                        use_angle_cls=False,
                        lang='en',
                        det_db_thresh=0.7,
                        rec_batch_num=1,
                        drop_score=0.7,
                        use_space_char=False,
                        max_text_length=15,
                        show_log=False
                    )
                    print("[INFO] ✅ 备用 PaddleOCR 初始化成功")
                except Exception as backup_e:
                    print(f"[ERROR] 备用 PaddleOCR 也初始化失败: {backup_e}")
                    raise backup_e
        return self._ocr

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str} or None if no text found
        """
        try:
            img = np.array(_img)
            result = self.ocr.ocr(img, cls=True)

            if debug:
                print(f"[DEBUG] OCR 原始结果: {result}")

            # 安全检查 OCR 结果
            if result is None:
                print("[WARNING] OCR 返回 None")
                return None

            if not isinstance(result, list) or len(result) == 0:
                print("[WARNING] OCR 返回空列表")
                return None

            # 检查第一层结果
            first_level = result[0]
            if first_level is None or not isinstance(first_level, list) or len(first_level) == 0:
                print("[WARNING] OCR 第一层结果为空")
                return None

            # 获取第一个检测到的文本
            first_detection = first_level[0]
            if not isinstance(first_detection, list) or len(first_detection) < 2:
                print(f"[WARNING] OCR 检测结果格式异常: {first_detection}")
                return None

            # 提取位置和文本
            location = first_detection[0]  # 位置信息
            text_info = first_detection[1]  # 文本信息

            if not isinstance(text_info, list) or len(text_info) < 1:
                print(f"[WARNING] OCR 文本信息格式异常: {text_info}")
                return None

            text = text_info[0]  # 实际文本

            if debug:
                print(f"[DEBUG] 提取的位置: {location}")
                print(f"[DEBUG] 提取的文本: {text}")

            return (location, text)

        except Exception as e:
            print(f"[ERROR] OCR 识别过程出错: {str(e)}")
            return None

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        image = _img
        inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                 "A picture with the other text"], images=image, return_tensors="pt", padding=True)
        inputs.to(self.device)

        outputs = self.clip_model(**inputs)
        logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
        probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
        if debug:
            print(probs)
        index = torch.argmax(probs, dim=1)
        return index

if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])
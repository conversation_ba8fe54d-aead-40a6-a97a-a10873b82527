import os
import time

import paddleocr
import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

# 无侵入式设置 Hugging Face 镜像站
def setup_hf_mirror():
    """设置 Hugging Face 镜像站，优先使用环境变量，然后使用默认镜像站"""
    hf_endpoint = os.getenv("HF_ENDPOINT", "https://hf-mirror.com")

    # 设置环境变量（如果还没设置的话）
    if "HF_ENDPOINT" not in os.environ:
        os.environ["HF_ENDPOINT"] = hf_endpoint

    # 禁用 SSL 验证以解决证书问题
    import ssl
    ssl._create_default_https_context = ssl._create_unverified_context

    # 动态修改 transformers 的默认端点
    if hasattr(file_utils, 'HUGGINGFACE_CO_RESOLVE_ENDPOINT'):
        file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT = hf_endpoint

    # 兼容不同版本的 transformers
    if hasattr(file_utils, 'HF_ENDPOINT'):
        file_utils.HF_ENDPOINT = hf_endpoint

    # 对于老版本的 transformers，直接修改内部常量
    try:
        import transformers.file_utils as tf_utils
        if hasattr(tf_utils, 'HUGGINGFACE_CO_RESOLVE_ENDPOINT'):
            tf_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT = hf_endpoint
        if hasattr(tf_utils, 'HUGGINGFACE_CO_URL_HOME'):
            tf_utils.HUGGINGFACE_CO_URL_HOME = hf_endpoint
        # 修改 URL 构建函数
        if hasattr(tf_utils, 'hf_bucket_url'):
            original_hf_bucket_url = tf_utils.hf_bucket_url
            def patched_hf_bucket_url(model_id, filename, subfolder=None, revision=None):
                url = original_hf_bucket_url(model_id, filename, subfolder, revision)
                return url.replace("https://huggingface.co", hf_endpoint)
            tf_utils.hf_bucket_url = patched_hf_bucket_url
    except:
        pass

    # 尝试修改 huggingface_hub 的配置
    try:
        import huggingface_hub
        if hasattr(huggingface_hub, 'constants'):
            if hasattr(huggingface_hub.constants, 'HUGGINGFACE_CO_URL_HOME'):
                huggingface_hub.constants.HUGGINGFACE_CO_URL_HOME = hf_endpoint
    except:
        pass

    return hf_endpoint

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        # 首先设置镜像站
        hf_endpoint = setup_hf_mirror()
        print(f'[INFO] 使用 Hugging Face 镜像站: {hf_endpoint}')

        self.ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang=language)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 验证镜像站配置
        print('huggingface镜像站:', file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT)
        print(f'[DEBUG] 当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')

        # 添加重试机制加载模型
        max_retries = 3
        self.clip_model = None
        self.clip_processor = None
        self.use_clip = False

        for i in range(max_retries):
            try:
                print(f'[DEBUG] 尝试加载CLIP模型 (第{i+1}次)')
                self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-large-patch14").to(self.device)
                self.clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-large-patch14")
                self.use_clip = True
                print('[DEBUG] ✅ CLIP模型加载成功')
                break
            except Exception as e:
                print(f'[DEBUG] ❌ 模型加载失败: {str(e)}')
                if i < max_retries - 1:
                    print(f'[DEBUG] 等待5秒后重试...')
                    time.sleep(5)
                else:
                    print('[WARNING] ⚠️ CLIP模型加载失败，将使用文本相似度降级方案')
                    self.use_clip = False
                    break

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str}
        """
        img = np.array(_img)
        result = self.ocr.ocr(img)
        if debug:
            print(result)
        if len(result[0]) == 0:
            return None
        else:
            location = result[0][0][0]
            text = result[0][0][1][0]
            return (location, text)

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        if self.use_clip and self.clip_model is not None:
            try:
                image = _img
                inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                         "A picture with the other text"], images=image, return_tensors="pt", padding=True)
                inputs.to(self.device)

                outputs = self.clip_model(**inputs)
                logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
                probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
                if debug:
                    print(probs)
                index = torch.argmax(probs, dim=1)
                return index
            except Exception as e:
                print(f'[WARNING] CLIP判断失败，使用文本相似度降级方案: {str(e)}')
                return self._fallback_text_similarity(_answer, _predict)
        else:
            return self._fallback_text_similarity(_answer, _predict)

    def _fallback_text_similarity(self, answer, predict):
        """
        降级方案：使用简单的文本相似度比较
        :parameter answer: 正确答案
        :parameter predict: 预测结果
        :return: 0 表示答案更相似，1 表示预测更相似，2 表示都不相似
        """
        import difflib

        # 转换为小写进行比较
        answer_lower = answer.lower().strip()
        predict_lower = predict.lower().strip()

        # 计算相似度
        similarity = difflib.SequenceMatcher(None, answer_lower, predict_lower).ratio()

        if debug:
            print(f'[DEBUG] 文本相似度: {similarity:.3f} (答案: "{answer}" vs 预测: "{predict}")')

        # 如果相似度大于0.8，认为是正确的
        if similarity > 0.8:
            return torch.tensor([0])  # 返回答案索引
        # 如果相似度在0.3-0.8之间，进行更细致的比较
        elif similarity > 0.3:
            # 检查是否包含主要关键词
            answer_words = set(answer_lower.split())
            predict_words = set(predict_lower.split())
            common_words = answer_words.intersection(predict_words)

            if len(common_words) > 0 and len(common_words) / max(len(answer_words), 1) > 0.5:
                return torch.tensor([0])  # 返回答案索引
            else:
                return torch.tensor([1])  # 返回预测索引
        else:
            return torch.tensor([2])  # 返回"其他"索引

if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])
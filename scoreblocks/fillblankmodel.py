import os
import time

import paddleocr
import numpy as np
from PIL import Image
import torch
from transformers import CLIPProcessor, CLIPModel, file_utils

# 无侵入式设置 Hugging Face 镜像站
def setup_hf_mirror():
    """设置 Hugging Face 镜像站，确保模型能够从镜像站下载"""
    hf_endpoint = os.getenv("HF_ENDPOINT", "https://hf-mirror.com")

    # 设置环境变量
    os.environ["HF_ENDPOINT"] = hf_endpoint
    os.environ["HUGGINGFACE_HUB_CACHE"] = "/root/.cache/huggingface"

    # 禁用 SSL 验证以解决证书问题
    import ssl
    ssl._create_default_https_context = ssl._create_unverified_context

    # 对于 transformers 4.12.0，需要修改这些关键属性
    from transformers import file_utils

    # 修改解析端点
    file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT = hf_endpoint

    # 修改 URL 前缀模板
    if hasattr(file_utils, 'HUGGINGFACE_CO_PREFIX'):
        file_utils.HUGGINGFACE_CO_PREFIX = f"{hf_endpoint}/{{model_id}}/resolve/{{revision}}/{{filename}}"

    # 修改 hf_bucket_url 函数
    if hasattr(file_utils, 'hf_bucket_url'):
        original_hf_bucket_url = file_utils.hf_bucket_url
        def patched_hf_bucket_url(model_id, filename, subfolder=None, revision=None):
            if revision is None:
                revision = "main"
            if subfolder is not None:
                filename = f"{subfolder}/{filename}"
            return f"{hf_endpoint}/{model_id}/resolve/{revision}/{filename}"
        file_utils.hf_bucket_url = patched_hf_bucket_url

    # 修改 cached_path 相关的 URL 构建
    if hasattr(file_utils, 'url_to_filename'):
        original_url_to_filename = file_utils.url_to_filename
        def patched_url_to_filename(url, etag=None):
            # 将 huggingface.co 替换为镜像站
            url = url.replace("https://huggingface.co", hf_endpoint)
            return original_url_to_filename(url, etag)
        file_utils.url_to_filename = patched_url_to_filename

    print(f'[INFO] 已配置 Hugging Face 镜像站: {hf_endpoint}')
    print(f'[INFO] HUGGINGFACE_CO_RESOLVE_ENDPOINT: {file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT}')
    if hasattr(file_utils, 'HUGGINGFACE_CO_PREFIX'):
        print(f'[INFO] HUGGINGFACE_CO_PREFIX: {file_utils.HUGGINGFACE_CO_PREFIX}')

    return hf_endpoint

debug = False

class model:
    def __init__(self, language:str="en"):
        """
        :parameter language: the language of the text, `ch`, `en`, `french`, `german`, `korean`, `japan`, type: str
        """
        # 首先设置镜像站
        hf_endpoint = setup_hf_mirror()
        print(f'[INFO] 使用 Hugging Face 镜像站: {hf_endpoint}')

        self.ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang=language)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 验证镜像站配置
        print('huggingface镜像站:', file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT)
        print(f'[DEBUG] 当前HF_ENDPOINT环境变量: {os.getenv("HF_ENDPOINT")}')

        # 尝试多种方式加载模型
        self.clip_model = None
        self.clip_processor = None
        self.use_clip = False

        # 方法1: 尝试从本地缓存加载
        local_model_path = '/root/.cache/huggingface/transformers/models--openai--clip-vit-large-patch14/snapshots/main'
        if os.path.exists(local_model_path):
            print('[INFO] 发现本地缓存，尝试从本地加载模型...')
            try:
                print('[DEBUG] 正在从本地加载 CLIP 模型...')
                self.clip_model = CLIPModel.from_pretrained(local_model_path)
                print('[DEBUG] 正在从本地加载 CLIP 处理器...')
                self.clip_processor = CLIPProcessor.from_pretrained(local_model_path)

                # 移动到设备
                self.clip_model = self.clip_model.to(self.device)
                self.use_clip = True
                print('[DEBUG] ✅ CLIP模型从本地缓存加载成功!')

            except Exception as e:
                print(f'[WARNING] 本地缓存加载失败: {str(e)}')

        # 方法2: 使用环境变量方式 (官方推荐)
        if not self.use_clip:
            print('[INFO] 尝试使用官方推荐的环境变量方式加载模型...')
            try:
                # 确保环境变量设置正确
                os.environ['HF_ENDPOINT'] = hf_endpoint

                print('[DEBUG] 正在下载/加载 CLIP 模型...')
                self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-large-patch14")
                print('[DEBUG] 正在下载/加载 CLIP 处理器...')
                self.clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-large-patch14")

                # 移动到设备
                self.clip_model = self.clip_model.to(self.device)
                self.use_clip = True
                print('[DEBUG] ✅ CLIP模型加载成功!')

            except Exception as e:
                print(f'[WARNING] 环境变量方式加载失败: {str(e)}')

        # 方法3: 降级方案
        if not self.use_clip:
            print('[WARNING] ⚠️ CLIP模型加载失败，将使用文本相似度降级方案')
            self.use_clip = False

    def recognize_text(self, _img:Image):
        """
        Predict the text from image
        :parameter img: image, type: np.ndarray
        :return: result, type: tuple{location: list, text: str}
        """
        img = np.array(_img)
        result = self.ocr.ocr(img)
        if debug:
            print(result)
        if len(result[0]) == 0:
            return None
        else:
            location = result[0][0][0]
            text = result[0][0][1][0]
            return (location, text)

    def judge_with_clip(self, _answer:str, _predict:str, _img:Image):
        """
        Use clip to judge which one is more similar to the Image
        :parameter answer: the answer text, type: str
        :parameter predict: the predict text, type: str
        :parameter img: image, type: np.ndarray
        :return: result, the index of the more similar text, type: int
        """
        if self.use_clip and self.clip_model is not None:
            try:
                image = _img
                inputs = self.clip_processor(text=[f"A picture with the text \"{_answer}\"", f"A picture with the text \"{_predict}\"",
                                         "A picture with the other text"], images=image, return_tensors="pt", padding=True)
                inputs.to(self.device)

                outputs = self.clip_model(**inputs)
                logits_per_image = outputs.logits_per_image  # this is the image-text similarity score
                probs = logits_per_image.softmax(dim=1)  # we can take the softmax to get the label probabilities
                if debug:
                    print(probs)
                index = torch.argmax(probs, dim=1)
                return index
            except Exception as e:
                print(f'[WARNING] CLIP判断失败，使用文本相似度降级方案: {str(e)}')
                return self._fallback_text_similarity(_answer, _predict)
        else:
            return self._fallback_text_similarity(_answer, _predict)

    def _fallback_text_similarity(self, answer, predict):
        """
        降级方案：使用简单的文本相似度比较
        :parameter answer: 正确答案
        :parameter predict: 预测结果
        :return: 0 表示答案更相似，1 表示预测更相似，2 表示都不相似
        """
        import difflib

        # 转换为小写进行比较
        answer_lower = answer.lower().strip()
        predict_lower = predict.lower().strip()

        # 计算相似度
        similarity = difflib.SequenceMatcher(None, answer_lower, predict_lower).ratio()

        if debug:
            print(f'[DEBUG] 文本相似度: {similarity:.3f} (答案: "{answer}" vs 预测: "{predict}")')

        # 如果相似度大于0.8，认为是正确的
        if similarity > 0.8:
            return torch.tensor([0])  # 返回答案索引
        # 如果相似度在0.3-0.8之间，进行更细致的比较
        elif similarity > 0.3:
            # 检查是否包含主要关键词
            answer_words = set(answer_lower.split())
            predict_words = set(predict_lower.split())
            common_words = answer_words.intersection(predict_words)

            if len(common_words) > 0 and len(common_words) / max(len(answer_words), 1) > 0.5:
                return torch.tensor([0])  # 返回答案索引
            else:
                return torch.tensor([1])  # 返回预测索引
        else:
            return torch.tensor([2])  # 返回"其他"索引

if __name__ == "__main__":
    """
    用于测试函数
    """
    debug = True
    import paddle
    print(paddle.device.is_compiled_with_cuda())
    model = model()
    while True:
        img_path = input("请输入图片路径: ")
        answer = input("请输入正确答案: ")
        img = Image.open(img_path)
        predict = model.recognize_text(img)[1]
        print("预测结果: ", predict)
        if (predict != answer):
            print("正确结果：", answer)
            index = model.judge_with_clip(answer, predict, img)
            print("判断结果: ", (answer, predict, "error")[index])
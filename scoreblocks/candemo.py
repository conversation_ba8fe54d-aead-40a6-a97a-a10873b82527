import torch
import numpy as np
from CAN.utils import load_config, load_checkpoint, compute_edit_distance
from CAN.dataset import Words
import sys,os
# sys.path.append('./CAN/')#
from CAN.models.infer_model import Inference
import cv2
class model:
    def __init__(self):
        # self.model_name=name
        # self.model=torch.load(path)
        base_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.normpath(os.path.join(base_dir, './CAN/config.yaml'))
        self.params=load_config(config_path)
        # params['device'] = device
        word_dict_path = os.path.normpath(os.path.join(base_dir, './CAN/words_dict.txt'))
        print(f'[debug] word_dict_path:{word_dict_path}')
        self.words = Words(word_dict_path)
        self.params['word_num'] = len(self.words)
        # 设置绝对路径避免工作目录问题
        self.params['word_path'] = word_dict_path
        self.params['device']='cpu'
        if 'use_label_mask' not in self.params:
            self.params['use_label_mask'] = False
        # print(params['decoder']['net'])
        self.model = Inference(self.params, draw_map=False)
        load_checkpoint(self.model,None,os.path.normpath(os.path.join(base_dir,'./CAN/checkpoints/demo.pth')))
        # self.model.cpu()
    def output(self,img_path):
        img=cv2.imread(img_path)
        image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        image = np.asarray(image)

        # print(np.shape(image))
        # 对数组进行维度处理（根据具体的神经网络测试需求进行维度调整）
        # processed_img_array = np.expand_dims(image, axis=0)
        image = torch.Tensor(255 - image) / 255
        image = image.unsqueeze(0).unsqueeze(0)
        pre,_, mae, mse=self.model(image,None,None)
        pre=self.words.decode(pre)
        print(pre)
        return pre

    def output_img(self, img):
        image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        image = np.asarray(image)

        # print(np.shape(image))
        # 对数组进行维度处理（根据具体的神经网络测试需求进行维度调整）
        # processed_img_array = np.expand_dims(image, axis=0)
        image = torch.Tensor(255 - image) / 255
        image = image.unsqueeze(0).unsqueeze(0)
        pre, _, mae, mse = self.model(image, None, None)
        pre = self.words.decode(pre)
        # print(pre)
        return pre

if __name__ == '__main__':
    model_instance = model()
    test_images = ['test_15.jpg', 'test_16.jpg', 'test_17.jpg', 'test_18.jpg', 'test_19.jpg', 'test_20.jpg']
    for img_name in test_images:
        print(f"\n测试图片 {img_name}:")
        image_path = f'./CAN/samples/{img_name}'
        model_instance.output(img_path=image_path)
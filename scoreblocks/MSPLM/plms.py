import torch
from torch import nn
from transformers import AutoModel
import torch.nn.functional as F
import os
import sys

# 配置 Hugging Face 镜像站
def configure_hf_mirror():
    """配置 Hugging Face 镜像站"""
    try:
        # 设置环境变量
        os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
        os.environ['HF_HUB_ENDPOINT'] = 'https://hf-mirror.com'
        os.environ['HUGGINGFACE_HUB_CACHE'] = '/root/.cache/huggingface'

        # 修改 transformers 库的下载 URL
        try:
            from transformers import file_utils

            # 设置镜像站端点
            hf_endpoint = 'https://hf-mirror.com'
            file_utils.HUGGINGFACE_CO_RESOLVE_ENDPOINT = hf_endpoint

            # 修改 hf_bucket_url 函数
            if hasattr(file_utils, 'hf_bucket_url'):
                original_hf_bucket_url = file_utils.hf_bucket_url

                def patched_hf_bucket_url(model_id, filename, subfolder=None, revision=None, mirror=None):
                    if revision is None:
                        revision = 'main'
                    if subfolder is not None:
                        filename = f'{subfolder}/{filename}'
                    return f'{hf_endpoint}/{model_id}/resolve/{revision}/{filename}'

                file_utils.hf_bucket_url = patched_hf_bucket_url

            return True

        except ImportError:
            return False

    except Exception as e:
        print(f"配置 Hugging Face 镜像站失败: {e}")
        return False

# 自动配置镜像站
configure_hf_mirror()


def init_weights(m):
    if isinstance(m, nn.Linear):
        torch.nn.init.xavier_uniform(m.weight)
        m.bias.data.fill_(7)


class mainplm(nn.Module):
    def __init__(self, args):
        super(mainplm, self).__init__()
        self.args = args
        self.plm_batch_size = 1

        # 尝试从本地缓存加载模型
        model_name = self.args['PLM']
        model_loaded = False

        # 定义可能的本地模型路径
        local_paths = [
            f"/root/.cache/huggingface/hub/{model_name}",
            f"/root/.cache/huggingface/transformers",
        ]

        # 首先尝试从本地路径加载
        for local_path in local_paths:
            if os.path.exists(local_path):
                try:
                    print(f"[INFO] 尝试从本地路径加载模型: {local_path}")
                    self.plm = AutoModel.from_pretrained(local_path, local_files_only=True)
                    print(f"[INFO] ✅ 模型从本地路径加载成功")
                    model_loaded = True
                    break
                except Exception as e:
                    print(f"[INFO] ❌ 从本地路径 {local_path} 加载模型失败: {str(e)}")
                    continue

        # 如果本地加载失败，尝试在线加载（使用镜像站配置）
        if not model_loaded:
            try:
                print(f"[INFO] 尝试在线加载模型: {model_name}")
                self.plm = AutoModel.from_pretrained(model_name)
                print(f"[INFO] ✅ 模型在线加载成功")
            except Exception as e:
                raise RuntimeError(f'无法加载模型 {model_name}: {str(e)}')

        for param in self.plm.embeddings.parameters():
            param.requires_grad = False
        for i in range(11):
            for param in self.plm.encoder.layer[i].parameters():
                param.requires_grad = False

        self.mlp = nn.Sequential(
            nn.Dropout(p=0.1),
            nn.Linear(self.plm.config.hidden_size, 1)
        )
        self.mlp.apply(init_weights)

    def forward(self, document_batch: torch.Tensor, device='cpu'):
        plm_output = torch.zeros(size=(document_batch.shape[0],
                                       min(document_batch.shape[1], self.plm_batch_size),
                                       self.plm.config.hidden_size),
                                 dtype=torch.float, device=device)
        for doc_id in range(document_batch.shape[0]):
            all_plm_output = self.plm(document_batch[doc_id][:self.plm_batch_size, 0],  # [1, 512]
                                      token_type_ids=document_batch[doc_id][:self.plm_batch_size, 1],
                                      attention_mask=document_batch[doc_id][:self.plm_batch_size, 2])
            plm_output[doc_id][:self.plm_batch_size] = all_plm_output.last_hidden_state[0][0].unsqueeze(0) # deberta:all_plm_output.last_hidden_state[0][0].unsqueeze(0) or bert:all_plm_output[1]
        prediction = self.mlp(plm_output.view(plm_output.shape[0], -1))
        assert prediction.shape[0] == document_batch.shape[0]
        return prediction


class chunkplm(nn.Module):
    def __init__(self, args):
        super(chunkplm, self).__init__()
        self.args = args

        # 尝试从本地缓存加载模型
        model_name = self.args['plm']
        model_loaded = False

        # 定义可能的本地模型路径
        local_paths = [
            f"/root/.cache/huggingface/hub/{model_name}",
            f"/root/.cache/huggingface/transformers",
        ]

        # 首先尝试从本地路径加载
        for local_path in local_paths:
            if os.path.exists(local_path):
                try:
                    print(f"[INFO] 尝试从本地路径加载模型: {local_path}")
                    self.plm = AutoModel.from_pretrained(local_path, local_files_only=True)
                    print(f"[INFO] ✅ 模型从本地路径加载成功")
                    model_loaded = True
                    break
                except Exception as e:
                    print(f"[INFO] ❌ 从本地路径 {local_path} 加载模型失败: {str(e)}")
                    continue

        # 如果本地加载失败，尝试在线加载（使用镜像站配置）
        if not model_loaded:
            try:
                print(f"[INFO] 尝试在线加载模型: {model_name}")
                self.plm = AutoModel.from_pretrained(model_name)
                print(f"[INFO] ✅ 模型在线加载成功")
            except Exception as e:
                raise RuntimeError(f'无法加载模型 {model_name}: {str(e)}')

        for param in self.plm.embeddings.parameters():
            param.requires_grad = False
        for i in range(12):
            for param in self.plm.encoder.layer[i].parameters():
                param.requires_grad = False

        self.dropout = nn.Dropout(p=0.1)
        self.lstm = nn.LSTM(self.plm.config.hidden_size, self.plm.config.hidden_size)
        self.mlp = nn.Sequential(
            nn.Dropout(p=0.1),
            nn.Linear(self.plm.config.hidden_size, 1)
        )
        self.w_omega = nn.Parameter(torch.Tensor(self.plm.config.hidden_size, self.plm.config.hidden_size))
        self.b_omega = nn.Parameter(torch.Tensor(1, self.plm.config.hidden_size))
        self.u_omega = nn.Parameter(torch.Tensor(self.plm.config.hidden_size, 1))

        nn.init.uniform_(self.w_omega, -0.1, 0.1)
        nn.init.uniform_(self.u_omega, -0.1, 0.1)
        nn.init.uniform_(self.b_omega, -0.1, 0.1)
        self.mlp.apply(init_weights)

    def forward(self, document_batch: torch.Tensor, device='cpu', plm_batch_size = 0):
        # output的chunk数是wordpiece / chunk_size
        plm_output = torch.zeros(size=(document_batch.shape[0],
                                       min(document_batch.shape[1],
                                           plm_batch_size),
                                       self.plm.config.hidden_size), dtype=torch.float, device=device)
        for doc_id in range(document_batch.shape[0]): # document_batch torch.Size([2, 12, 3, 90])
            plm_output[doc_id][:plm_batch_size] = self.dropout(
                self.plm(document_batch[doc_id][:plm_batch_size, 0],
                         token_type_ids=document_batch[doc_id][
                                        :plm_batch_size, 1],
                         attention_mask=document_batch[doc_id][
                                        :plm_batch_size, 2])[1])
        output, (_, _) = self.lstm(plm_output.permute(1, 0, 2))
        output = output.permute(1, 0, 2)
        # (batch_size, seq_len, num_hiddens)
        attention_w = torch.tanh(torch.matmul(output, self.w_omega) + self.b_omega)
        attention_u = torch.matmul(attention_w, self.u_omega)  # (batch_size, seq_len, 1)
        attention_score = F.softmax(attention_u, dim=1)  # (batch_size, seq_len, 1)
        attention_hidden = output * attention_score  # (batch_size, seq_len, num_hiddens)
        attention_hidden = torch.sum(attention_hidden, dim=1)  # 加权求和 (batch_size, num_hiddens)
        prediction = self.mlp(attention_hidden)
        assert prediction.shape[0] == document_batch.shape[0]
        return prediction

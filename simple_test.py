#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的功能验证测试
"""

import requests
import time

def test_api_functionality():
    """测试API功能"""
    base_url = "http://192.168.1.10:58000"
    api_base_url = f"{base_url}/api"
    
    print("=== API功能测试 ===")
    
    # 1. 测试主页访问
    print("1. 测试主页访问...")
    try:
        response = requests.get(base_url, timeout=10)
        print(f"   主页状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 主页访问正常")
        else:
            print("   ❌ 主页访问异常")
            return False
    except Exception as e:
        print(f"   ❌ 主页访问失败: {e}")
        return False
    
    # 2. 测试训练状态API
    print("2. 测试训练状态API...")
    try:
        response = requests.get(f"{api_base_url}/train/status", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应消息: {data.get('msg', 'N/A')}")
            print("   ✅ 训练状态API正常")
        else:
            print("   ❌ 训练状态API异常")
            return False
    except Exception as e:
        print(f"   ❌ 训练状态API失败: {e}")
        return False
    
    # 3. 测试训练页面访问
    print("3. 测试训练页面访问...")
    try:
        response = requests.get(f"{base_url}/teacher/train", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 训练页面访问正常")
        else:
            print("   ❌ 训练页面访问异常")
            return False
    except Exception as e:
        print(f"   ❌ 训练页面访问失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始功能验证测试...")
    print("=" * 60)
    
    # 测试API功能
    api_success = test_api_functionality()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"API功能测试: {'✅ 通过' if api_success else '❌ 失败'}")
    
    if api_success:
        print("\n🎉 核心功能验证通过！")
        print("修复的功能包括:")
        print("  ✅ 动态端口配置")
        print("  ✅ API端点正常工作")
        print("  ✅ 训练页面访问")
    else:
        print("\n❌ 部分功能存在问题，需要进一步检查")
    
    return api_success

if __name__ == "__main__":
    main()
